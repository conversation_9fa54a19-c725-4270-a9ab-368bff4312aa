import { Component, Input } from '@angular/core';
import { Control<PERSON>ontainer, NgModelGroup } from '@angular/forms';
import { MaterialsMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign, MaterialMetricsModel, MaterialWeight } from '@com/services/cell-design.service';
import { Material, InactiveMaterialModel, MaterialType } from '@com/services/material.service';
import { IdLookup } from '@com/app/cell-design-page/types';
import { validateGroupSumErrorActualNumber } from '@com/app/shared/directive/validators/validate-group-sum.directive';
import { deepCloneDto } from '@com/utils/object';

export interface MaterialOption extends InactiveMaterialModel {
    disabled: boolean;
}

const FORM_GROUP_NAMES = {
    anodeWeight: 'materials_formGroup_anodeWeight',
    cathodeWeight: 'materials_formGroup_cathodeWeight',
} as const;

const CONTROL_NAMES = {
    // The control names should be unique, otherwise it will break "comValidateGroupSumDirective" and it wont work correctly.
    anodeActiveMaterialWeight: 'materials_anodeActiveMaterialWeight',
    anodeBinderVersion: 'materials_anodeBinderVersion',
    anodeBinderPpaWeight: 'materials_anodeBinderPpaWeight',
    anodeConductiveAdditiveVersion: 'materials_anodeConductiveAdditiveVersion',
    anodeConductiveAdditiveWeight: 'materials_anodeConductiveAdditiveWeight',
    anodeisGroup14: 'materials_anodeisGroup14',

    cathodeActiveMaterialWeight: 'materials_cathodeActiveMaterialWeight',
    cathodeBinderVersion: 'materials_cathodeBinderVersion',
    cathodeBinderPpaWeight: 'materials_cathodeBinderPpaWeight',
    cathodeConductiveAdditiveVersion: 'materials_cathodeConductiveAdditiveVersion',
    cathodeConductiveAdditiveWeight: 'materials_cathodeConductiveAdditiveWeight',

    electrolyte: 'materials_electrolyte',
    separator: 'materials_separator',
    aluminiumCurrentCollector: 'materials_aluminiumCurrentCollector',
    copperCurrentCollector: 'materials_copperCurrentCollector',
} as const;

@Component({
    selector: 'com-materials-editor',
    templateUrl: 'materials-editor.component.html',
    styleUrls: ['./materials-editor.component.scss'],
    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class MaterialsEditorComponent {
    @Input()
    public loading: boolean;

    @Input()
    public materials: IdLookup<Material> = {};

    @Input()
    public set design(design: CellDesign | undefined) {
        this._design = design;

        if (this.design) {
            this.calculateAnodeWeightPercentages();
            this.calculateCathodeWeightPercentages();
        }
    }

    public get design(): CellDesign | undefined {
        return this._design;
    }

    @Input()
    public set metrics(metrics: MaterialsMetrics | undefined) {
        this._metrics = metrics;

        if (this.metrics) {
            this.binderMaterialsWeightPercent = 0;
            this.binderMaterialsWeightPercent += this.getMaterialsWeightsPercentagesSum(
                this.metrics.prelithiation.binderMaterials
            );

            this.conductiveMaterialsWeightPercent = 0;
            this.binderMaterialsWeightPercent += this.getMaterialsWeightsPercentagesSum(
                this.metrics.prelithiation.conductiveAdditiveMaterials
            );
        }
    }

    public get metrics(): MaterialsMetrics | undefined {
        return this._metrics;
    }

    @Input() public set inactiveMaterials(inactiveMaterials: InactiveMaterialModel[] | null) {
        this.anodeBinderMaterialsAllOptions = this.getFilteredMaterialOptions(
            inactiveMaterials,
            MaterialType.binder,
            this.design!.anodeBinderMaterials,
            true
        );
        this.anodeBinderDensities = new Map<string, number>();
        this.anodeBinderMaterialsAllOptions.forEach((option) => {
            this.anodeBinderDensities.set(option.id, option.density);
        });

        this.cathodeBinderMaterialsAllOptions = this.getFilteredMaterialOptions(
            inactiveMaterials,
            MaterialType.binder,
            this.design!.cathodeBinderMaterials,
            false
        );
        this.cathodeBinderDensities = new Map<string, number>();
        this.cathodeBinderMaterialsAllOptions.forEach((option) => {
            this.cathodeBinderDensities.set(option.id, option.density);
        });

        this.anodeConductiveMaterialsAllOptions = this.getFilteredMaterialOptions(
            inactiveMaterials,
            MaterialType.conductiveAdditive,
            this.design!.anodeConductiveAdditiveMaterials,
            true
        );
        this.anodeConductiveDensities = new Map<string, number>();
        this.anodeConductiveMaterialsAllOptions.forEach((option) => {
            this.anodeConductiveDensities.set(option.id, option.density);
        });

        this.cathodeConductiveMaterialsAllOptions = this.getFilteredMaterialOptions(
            inactiveMaterials,
            MaterialType.conductiveAdditive,
            this.design!.cathodeConductiveAdditiveMaterials,
            false
        );
        this.cathodeConductiveDensities = new Map<string, number>();
        this.cathodeConductiveMaterialsAllOptions.forEach((option) => {
            this.cathodeConductiveDensities.set(option.id, option.density);
        });

        this.electrolyteMaterialsAllOptions =
            inactiveMaterials?.filter((m) => m.type === MaterialType.electrolyte) ?? [];

        this.separatorMaterialsAllOptions = inactiveMaterials?.filter((m) => m.type === MaterialType.separator) ?? [];

        this.aluminiumCurrentCollectorMaterialsAllOptions =
            inactiveMaterials?.filter((m) => m.type === MaterialType.currentCollector && m.isCathode) ?? [];

        this.copperCurrentCollectorMaterialsAllOptions =
            inactiveMaterials?.filter((m) => m.type === MaterialType.currentCollector && m.isAnode) ?? [];
    }

    public anodeBinderMaterialsAllOptions: MaterialOption[];
    public cathodeBinderMaterialsAllOptions: MaterialOption[];
    public anodeConductiveMaterialsAllOptions: MaterialOption[];
    public cathodeConductiveMaterialsAllOptions: MaterialOption[];

    public electrolyteMaterialsAllOptions: InactiveMaterialModel[];
    public separatorMaterialsAllOptions: InactiveMaterialModel[];
    public aluminiumCurrentCollectorMaterialsAllOptions: InactiveMaterialModel[];
    public copperCurrentCollectorMaterialsAllOptions: InactiveMaterialModel[];

    public anodeBinderDensities: Map<string, number> = new Map<string, number>();
    public cathodeBinderDensities: Map<string, number> = new Map<string, number>();
    public anodeConductiveDensities: Map<string, number> = new Map<string, number>();
    public cathodeConductiveDensities: Map<string, number> = new Map<string, number>();

    public formGroupNames = FORM_GROUP_NAMES;
    public controlNames = CONTROL_NAMES;

    public sumValidationErrorName = validateGroupSumErrorActualNumber;
    public binderMaterialsWeightPercent = 0;
    public conductiveMaterialsWeightPercent = 0;

    public readonly minFractionDigits = 2;
    public readonly maxFractionDigits = 3;
    public readonly desiredSum = 100;

    private _design: CellDesign | undefined = undefined;
    private _metrics: MaterialsMetrics | undefined = undefined;

    public changeBinderVersion(allOptions: MaterialOption[], designSelectedMaterials: MaterialMetricsModel[]): void {
        this.disableOptionsBasedOnDesign(allOptions, designSelectedMaterials);
    }

    public addVersion(allOptions: MaterialOption[], designSelectedMaterials: MaterialMetricsModel[]): void {
        this.addOptionToDesign(allOptions, designSelectedMaterials);
    }

    public removeVersion(
        optionId: string,
        allOptions: MaterialOption[],
        designSelectedMaterials: MaterialMetricsModel[]
    ): void {
        this.removeOptionFromDesign(optionId, allOptions, designSelectedMaterials);
    }

    public applyAdditionalAnodeValues = (material?: MaterialWeight): void => {
        const anodeTotalWeightPercentage = this.calculateAnodeWeightPercentages();
        const anodeAdjustmentValue = this.desiredSum - anodeTotalWeightPercentage;

        if (material) {
            material.weightPercent += anodeAdjustmentValue;
        } else if (this.design) {
            this.design.activeMaterialAnodeWeightPercent += anodeAdjustmentValue;
        }
    };

    public applyAdditionalCathodeValues = (material?: MaterialWeight): void => {
        const cathodeTotalWeightPercentage = this.calculateCathodeWeightPercentages();
        const cathodeAdjustmentValue = this.desiredSum - cathodeTotalWeightPercentage;

        if (material) {
            material.weightPercent += cathodeAdjustmentValue;
        } else if (this.design) {
            this.design.activeMaterialCathodeWeightPercent += cathodeAdjustmentValue;
        }
    };

    public removeAdditionalAnodeValues = (material?: MaterialWeight): void => {
        const anodeTotalWeightPercentage = this.calculateAnodeWeightPercentages();
        const anodeAdjustmentValue = anodeTotalWeightPercentage - this.desiredSum;

        if (material) {
            material.weightPercent -= anodeAdjustmentValue;
            material.weightPercent < 0 ? (material.weightPercent = 0) : material.weightPercent;
        } else if (this.design) {
            this.design.activeMaterialAnodeWeightPercent -= anodeAdjustmentValue;
            this.design.activeMaterialAnodeWeightPercent < 0
                ? (this.design.activeMaterialAnodeWeightPercent = 0)
                : this.design.activeMaterialAnodeWeightPercent;
        }
    };

    public removeAdditionalCathodeValues = (material?: MaterialWeight): void => {
        const cathodeTotalWeightPercentage = this.calculateCathodeWeightPercentages();
        const cathodeAdjustmentValue = cathodeTotalWeightPercentage - this.desiredSum;

        if (material) {
            material.weightPercent -= cathodeAdjustmentValue;
            material.weightPercent < 0 ? (material.weightPercent = 0) : material.weightPercent;
        } else if (this.design) {
            this.design.activeMaterialCathodeWeightPercent -= cathodeAdjustmentValue;
            this.design.activeMaterialCathodeWeightPercent < 0
                ? (this.design.activeMaterialCathodeWeightPercent = 0)
                : this.design.activeMaterialCathodeWeightPercent;
        }
    };

    public calculateAnodeWeightPercentages(): number {
        const anodeTotalWeightPercentage = this.calculateMaterialWeightPercentage(
            this.design!.anodeBinderMaterials,
            this.design!.anodeConductiveAdditiveMaterials,
            this.design!.activeMaterialAnodeWeightPercent
        );

        return anodeTotalWeightPercentage;
    }

    public calculateCathodeWeightPercentages(): number {
        const cathodeTotalWeightPercentage = this.calculateMaterialWeightPercentage(
            this.design!.cathodeBinderMaterials,
            this.design!.cathodeConductiveAdditiveMaterials,
            this.design!.activeMaterialCathodeWeightPercent
        );

        return cathodeTotalWeightPercentage;
    }

    public lookupMaterial(id: string): Material | undefined {
        return this.materials[id];
    }

    private calculateMaterialWeightPercentage(
        binderMaterials: MaterialMetricsModel[],
        conductiveMaterials: MaterialMetricsModel[],
        activeMaterial: number
    ): number {
        let totalWeightPercentage = 0;
        totalWeightPercentage += this.getMaterialsWeightsPercentagesSum(binderMaterials);
        totalWeightPercentage += this.getMaterialsWeightsPercentagesSum(conductiveMaterials);
        totalWeightPercentage += activeMaterial;

        return totalWeightPercentage;
    }

    private getMaterialsWeightsPercentagesSum(materials: MaterialMetricsModel[]): number {
        return materials.reduce((accumulated, currentMaterial) => {
            accumulated += currentMaterial.weightPercent;

            return accumulated;
        }, 0);
    }

    private getFilteredMaterialOptions(
        inactiveMaterials: InactiveMaterialModel[] | null,
        materialType: MaterialType,
        designMetrics: MaterialMetricsModel[],
        isAnode: boolean
    ): MaterialOption[] {
        const materialOptions = deepCloneDto(
            inactiveMaterials?.filter(
                (option) => option.type === materialType && (isAnode ? option.isAnode : option.isCathode)
            ) as MaterialOption[]
        );

        materialOptions.forEach((option) => {
            if (designMetrics.some((design) => option.id === design.materialId)) {
                option.disabled = true;
            }
        });

        return materialOptions;
    }

    private addOptionToDesign(materialAllOptions: MaterialOption[], designMaterials: MaterialMetricsModel[]): void {
        const optionToAdd = materialAllOptions.find((option) => {
            const currentMatch = designMaterials.find((selectedOption) => selectedOption.materialId === option.id);

            return currentMatch === undefined;
        }) as MaterialOption;

        if (optionToAdd) {
            optionToAdd.disabled = true;
            designMaterials.push({
                materialId: optionToAdd.id,
                weightPercent: 0,
                density: optionToAdd.density,
            });
        }

        this.disableOptionsBasedOnDesign(materialAllOptions, designMaterials);
    }

    private removeOptionFromDesign(
        optionId: string,
        materialAllOptions: MaterialOption[],
        designMaterials: MaterialMetricsModel[]
    ): void {
        const index = designMaterials.findIndex((x) => x.materialId === optionId);
        if (index >= 0) {
            designMaterials.splice(index, 1);
        }

        this.disableOptionsBasedOnDesign(materialAllOptions, designMaterials);
    }

    private disableOptionsBasedOnDesign(allOptions: MaterialOption[], design: MaterialMetricsModel[]): void {
        allOptions.forEach((option) => {
            option.disabled = design.some((m) => m.materialId === option.id);
        });
    }
}

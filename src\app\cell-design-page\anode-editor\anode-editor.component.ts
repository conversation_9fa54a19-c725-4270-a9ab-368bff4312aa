import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { Control<PERSON>ontainer, NgModelGroup } from '@angular/forms';

import { LINE_CHART_OPTIONS } from '@com/const';
import { CellDesignMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign } from '@com/services/cell-design.service';
import { Material, MaterialVersionModel } from '@com/services/material.service';
import { deepEquals } from '@com/utils/deep-equals';
import { deepCloneDto } from '@com/utils/object';
import {
    validateQValuesMinimumError,
    validateQValuesComparisonError,
} from '@com/app/shared/directive/validators/validate-q-values.directive';
import { validateGroupSumErrorActualNumber } from '@com/app/shared/directive/validators/validate-group-sum.directive';

const FORM_GROUP_NAMES = {
    anodeMaterial: 'anode_formGroup',
    anodeQValues: 'anode_q_values_formGroup',
} as const;

export const validateFieldRequiredError = 'required';

const CONTROL_NAMES = {
    anodeMaterial1Weight: 'anode_material1_weight',
    anodeMaterial2Weight: 'anode_material2_weight',
} as const;

@Component({
    selector: 'com-anode-editor',
    templateUrl: './anode-editor.component.html',

    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class AnodeEditorComponent implements OnInit, OnChanges {
    @Input()
    public get materials(): Material[] | null {
        return this._materials;
    }

    public set materials(value: Material[] | null) {
        this._materials = value;
        this.blendableMaterials = this.materials?.filter((material) => material.blendable) ?? null;
        this.materialMap = new Map();

        this._materials?.forEach((material) => {
            this.materialMap.set(material.id, material.versions);
        });
    }

    @Input()
    public design!: CellDesign | null;

    @Input()
    public metrics!: CellDesignMetrics | null;

    @Input()
    public loading!: boolean;

    // chart.js have the chart options defined as any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public options: any = {
        ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),
        plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),
        scales: deepCloneDto(LINE_CHART_OPTIONS.scales),
    };

    public materialMap: Map<string, MaterialVersionModel[]> = new Map<string, MaterialVersionModel[]>();

    public blendableMaterials: Material[] | null = null;

    public get remainingBlendableMaterials(): Material[] {
        const result = this.blendableMaterials?.filter((m) => m.id !== this.design?.anodeMaterials[0].materialId) ?? [];
        if (this._remainingBlendableMaterials === null || !deepEquals(result, this._remainingBlendableMaterials)) {
            this._remainingBlendableMaterials = result;
        }

        return this._remainingBlendableMaterials;
    }

    public formGroupNames = FORM_GROUP_NAMES;
    public controlNames = CONTROL_NAMES;

    public highlightedColumns: number[] = [];

    public readonly desiredSum = 100;
    public readonly qValuesMinimumError = validateQValuesMinimumError;
    public readonly qValuesComparisonError = validateQValuesComparisonError;
    public readonly groupSumErrorName = validateGroupSumErrorActualNumber;
    public readonly fieldRequiredError = validateFieldRequiredError;

    private _materials: Material[] | null = null;

    private _remainingBlendableMaterials: Material[] | null = null;

    public toggleDeveloperMode(input: boolean): void {
        if (this.design && this.blendableMaterials) {
            this.design.anodeMaterials = input
                ? [
                      {
                          materialId: this.design.anodeMaterials[0].materialId,
                          materialVersionId: this.design.anodeMaterials[0].materialVersionId,
                          weightPercent: 50,
                      },
                      {
                          materialId: this.blendableMaterials[0].id,
                          materialVersionId: this.blendableMaterials[0].versions
                              ? this.blendableMaterials[0].versions[0].id
                              : null,
                          weightPercent: 50,
                      },
                  ]
                : [
                      {
                          materialId: this.design.anodeMaterials[0].materialId,
                          materialVersionId: this.design.anodeMaterials[0].materialVersionId,
                          weightPercent: 100,
                      },
                  ];
        }
    }

    public ngOnInit(): void {
        this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.x.title.text = $localize`:@@axisTitle.capacityAnode: Capacity / mAh/g(AAM)`;

        this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.y.title.text = $localize`:@@axisTitle.voltage: Voltage vs. Li / V`;
        this.options.scales.y.min = 0;
        this.options.scales.y.max = 1.5;
    }

    public ngOnChanges(changes: SimpleChanges): void {
        if (changes['metrics']) {
            this.updateHighlightedColumns();
        }
    }

    private updateHighlightedColumns(): void {
        const indexOfBlendColumn = this.metrics?.anode.summary.headers.indexOf('Blend');
        if ((this.design?.anodeMaterials ?? []).length > 1) {
            if (indexOfBlendColumn !== -1 && indexOfBlendColumn !== undefined) {
                if (!this.highlightedColumns.includes(indexOfBlendColumn)) {
                    this.highlightedColumns.push(indexOfBlendColumn);
                }
            }
        } else {
            this.highlightedColumns = [];
        }
    }
}

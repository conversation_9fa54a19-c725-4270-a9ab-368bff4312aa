{"locale": "en-GB", "translations": {"tab.toleranceAnalysis": "Tolerance Analysis", "createTolerance.noToleranceMessage": "Tolerance analysis not available.", "createTolerance.parameterSet": "Parameter Set", "createTolerance.toleranceVariables": "Tolerance Variables", "createTolerance.minimumValue": "Minimum Value", "createTolerance.standardValue": "Standard Value", "createTolerance.maximumValue": "Maximum Value", "createTolerance.minimumValueValidation": "Minimum value must be less than maximum value", "createTolerance.missingStandardValue": "Value for this tolerance variable is not specified in the cell design. To calculate tolerance, you need to set this value first.", "createTolerance.calculateTolerance": "Calculate Tolerance", "createTolerance.toleranceResults": "Tolerance Results", "createTolerance.variableResults": "Variable Results", "createTolerance.valueType": "Value Type", "createTolerance.valueTypeAbsolute": "Absolute", "createTolerance.valueTypeRelative": "Relative", "createTolerance.valueTypeRelativePercentage": "Relative Percentage", "createTolerance.chainedResults": "Chained Results", "createTolerance.minimumValueResult": "Minimum Value", "createTolerance.standardValueResult": "Standard Value", "createTolerance.maximumValueResult": "Maximum Value", "createTolerance.calculating": "Calculating tolerance...", "createTolerance.variableCategory": "Variable Category", "createTolerance.resultsFor": "Results for:", "axisTitle.capacityAnode": "Capacity / mAh/g(AAM)", "axisTitle.capacityCathode": "Capacity / mAh/g(CAM)", "axisTitle.cellPotential": "Cell potential / V", "axisTitle.voltage": "Voltage vs. Li / V", "balancing.hysteresis.label": "Hysteresis", "balancing.npRatioFirst.input.label": "N/P Ratio First", "balancing.npRatioRev.label": "N/P Ratio Reversible", "balancing.uMax.input.label": "Umax", "balancing.uMin.input.label": "<PERSON><PERSON>", "bom.input.dataSet.label": "Data set", "bom.input.scrapMaterial.label": "Scrap material", "cellDesign.topbar.designId": "DesignID: {$INTERPOLATION}", "cellDesign.topbar.name": "Name: {$INTERPOLATION}", "cellDesign.topbar.releaseStatus": "Release Status: {$INTERPOLATION}", "cellDesign.topbar.partNumber": "Part Number: {$INTERPOLATION}", "cellDesign.topbar.capacityAh": "Capacity: {$INTERPOLATION} Ah", "cellDesign.topbar.energyContentWh": "Energy Content: {$INTERPOLATION} Wh", "cellDesign.topbar.energyDensityVolumentric": "Energy Density Volumetric: {$INTERPOLATION} Wh/l", "cellDesign.topbar.energyDensityGravimetric": "Energy Density Gravimetric: {$INTERPOLATION} Wh/kg", "cellDesign.topbar.packageWeight": "Whole Cell: {$INTERPOLATION} g", "cell.anodeLengthTotal": "Total Length Anode", "cell.anodeOverhang": "<PERSON><PERSON>", "cell.anodeWindingCount": "<PERSON><PERSON>", "cell.assemblyClearance": "Assembly Clearance", "cell.coatingSurfaces": "Coating Surfaces", "cell.caseWeight": "Case Weight (incl. Tabs)", "cell.cathodeLengthTotal": "Total Length Cathode", "cell.cathodeWindingCount": "Cathode Winding Count", "cell.cellCoreDiameter": "Cell Core Diameter", "cell.cellDiameter": "Cell Diameter", "cell.cellFormat": "Cell Format", "cell.cellHeight": "Cell Height", "cell.cellLayerDiameterMax": "Max. total Diameter Cell Layers", "cell.cellLayerDiameterTotal": "Diameter Cell Layers", "cell.cellLength": "Cell Length", "cell.cellThickness": "Cell Thickness", "cell.cellType.cylinder": "<PERSON><PERSON><PERSON>", "cell.celltype.label": "Cell Type", "cell.cellType.pouch": "Pouch", "cell.cellType.prisma": "Prism", "cell.cellVolume": "Cell Volume", "cell.cellWidth": "Cell Width", "cell.chargePerLayerC10": "Charge per Layer C/1O", "cell.chargePerLayerC3": "Charge per Layer C/3", "cell.coatingAreaAnode": "Coating Area Anode", "cell.coatingAreaCathode": "Coating Area Kathode", "cell.coatingAreaSeparator": "Coating Area Separator", "cell.coatingLengthAnode": "Coating Length Anode", "cell.coatingLengthCathode": "Coating Length Kathode", "cell.coatingLengthSeparator": "Coating Length Separator", "cell.coatingWidthAnode": "Coating Width Anode", "cell.coatingWidthCathode": "Coating Width Kathode", "cell.coatingWidthSeparator": "Coating Width Separator", "cell.editFormat": "Edit Format?", "cell.electrolyteSwelling": "Elektrolyte Swelling", "cell.energyPerLayerC10": "Energy per Layer C/1O", "cell.energyPerLayerC3": "Energy per Layer C/3", "cell.hasTwoSubstacks": "Has Two Substacks?", "cell.housingThickness": "Pouch/Housing Thickness", "cell.layerFreeSpace": "Layer Free Space", "cell.layerFreeSpaceOverThickness": "Layer Free Space / Cell Layer Thickness", "cell.maxTotalLayerThickness": "Max. Total Layer Thickness", "cell.numberOfActiveLayers": "Active Layer Count", "cell.numberOfLayersAnode": "Layer Count <PERSON>", "cell.numberOfLayersCathode": "Layer Count <PERSON>", "cell.numberOfLayersSeparator": "Layer Count <PERSON>", "cell.separatorWindingCount": "Separator Winding <PERSON>", "cell.swellingBuffer": "Swelling Buffer", "cell.totalAreaSeparator": "Total Area Separator", "cell.totalLayerThickness": "Total Layer Thickness", "cell.cellGeometry": "Cell Geometry", "cell.cellLayerCalculation": "Cell Layer Calculation", "chemicals.anode.prelithiationCapacity.input.label": "Prelithiation", "chemicals.common.developerMode.checkbox.label": "Developer Mode", "chemicals.common.material.input.label": " Material {$INTERPOLATION} ", "chemicals.common.material.ratio.input.label": " Material {$INTERPOLATION} Ratio ", "chemicals.common.material.version.input.label": " Material {$INTERPOLATION} Version ", "chemicals.common.qAim.input.label": "Target Qrev C/10", "chemicals.common.qAim1.input.label": "Target Q1st C/10", "common.aging": "Aging", "common.amountOfElectrolyte": "Amount of electrolyte", "common.amperHour": "{$INTERPOLATION} Ah", "common.anode": "Anode", "common.anodeDensity": "Anode Density", "common.cancel": "Cancel", "common.create": "Create", "common.capacity": "Capacity", "anode.qValues.comparison.error": "Target Q1st C/10 must be greater than Target Qrev C/10", "anode.qValues.minimumValue.error": "Both Target Qrev C/10 and Target Q1st C/10 values must be at least 300 mAh/g", "cathode.qValues.comparison.error": "Target Q1st C/10 must be greater than Target Qrev C/10", "cathode.qValues.minimumValue.error": "Both Target Qrev C/10 and Target Q1st C/10 values must be at least 150 mAh/g", "common.cathode": "Cathode", "common.cathodeDensity": "Cathode Density", "common.cellDimensions": "Cell dimensions", "common.cellsTotal": "Cells total", "common.confirm": "Confirm", "common.confirmation": "Confirmation", "common.cRate10Symbol": "C/10", "common.cRate3Symbol": "C/3", "common.delete": "Delete", "common.deleteConfirmationMessage": "Are you sure that you want to delete {$name}?", "common.density": "Density", "common.description": "Description", "common.design": "Design", "common.designAlreadyExitsts": "Design cannot be saved since it already exists. Do you want to open it?", "common.designsCannotBeEdited": "Designs cannot be edited. Do you want to save your changes as a new design?", "common.editingItem": "Editing {$INTERPOLATION}", "common.editMetadata": "<PERSON>", "common.editOrRelease": "Edit Metadata/Release", "common.electrolyteSeparator": "Electrolyte + Separator", "common.energyHold": "Energy hold", "common.discharge": "Discharge", "common.errorWithCode": "Error Code: {$statusCode}, Message: {$message}", "common.errorWithMessage": "Error: {$message}", "common.euro": "{$INTERPOLATION} €", "common.euroPerKilowattHour": "{$INTERPOLATION} €/kWh", "common.export": "Export", "common.genericErrorMessage": "An error has occurred: {$INTERPOLATION}", "common.validationErrorTitle": "Design Validation Error", "common.validationErrorMessage": "Your design contains validation errors. Please fix the errors marked with * to calculate metrics.", "common.gram": "{$INTERPOLATION} g", "common.informationFromManufacturer": "Information from manufacturer", "common.itemHasBeenDeleted": "{$PH} has been deleted successfully!", "common.kilogram": "{$INTERPOLATION} kg", "common.kiloWatHour": "{$INTERPOLATION} kWh", "common.charge": "Charge", "common.litre": "{$INTERPOLATION} l", "common.materialPricePerKwH": "Material price per kWh", "common.microMeter": "{$INTERPOLATION} µm", "common.milliAmperHourPerSquareCentiMeter": "{$INTERPOLATION} mAh/cm²", "common.milliGramPerSquareCentiMeter": "{$INTERPOLATION} mg/cm²", "common.milliVolts": "{$INTERPOLATION} mV", "common.moduleCount": "Module Count", "common.name": "Name", "common.nominalVoltage": "Nominal voltage", "common.notReleased": "Not Released", "common.open": "Open", "common.outOfBalancing": "Out of balancing", "common.packDesign": "Pack Design", "common.packingWeight": "Packing weight", "common.partNumber": "Part Number", "common.percentageNumber": "{$INTERPOLATION} %", "common.poreVolume": "Pore Volume", "common.porosity": "Porosity", "common.price": "Price", "common.projectName": "Project Name", "common.projectState": "Project Status", "common.released": "Released", "common.releasedBy": "Released By", "common.releasedDate": "Release Date", "common.releaseStatus": "Release Status", "common.safety": "Safety", "common.save": "Save", "common.saveNewDesign": "Save new design", "common.searchDesigns": "Search Designs", "common.soc": "SoC", "common.socMaterial": "SoC Material {$INTERPOLATION}", "common.startNewDesign": "Start new Design", "common.success": "Success", "common.unexpectedError": "Unexpected Error", "common.units.amperHours": "{$INTERPOLATION} Ah", "common.units.micrometer": "{$INTERPOLATION} μm", "common.units.mililitre": "{$INTERPOLATION} ml", "common.units.mililitrePerAmperHour": "{$INTERPOLATION} ml/Ah", "common.units.millimeter": "{$INTERPOLATION} mm", "common.units.nanometersPerAmperhour": "{$INTERPOLATION} nm/Ah", "common.units.squareMeter": "{$INTERPOLATION} m²", "common.units.squareMillimeters": "{$INTERPOLATION} mm²", "common.units.percentages": "{$INTERPOLATION} %", "common.units.wattHours": "{$INTERPOLATION} Wh", "common.volt": "{$INTERPOLATION} V", "common.voltage": "Voltage", "common.voltageLithium": "Voltage vs. Li", "common.warning": "Warning", "common.wattHour": "{$INTERPOLATION} Wh", "common.watthourPerKilogram": "{$INTERPOLATION} Wh/kg", "common.watthourPerLitre": "{$INTERPOLATION} Wh/l", "common.createdAt": "Created At", "common.createdBy": "Created By", "createOptimization.createNewOptimizedDesign": "Create new optimized design", "createOptimization.noOptimizationMessage": "Optimization of anode material blend not available. Current optimization is only optimizing SCC Content.", "createOptimization.parameterSet": "Parametеr Set", "createOptimization.optimizationVariables": "Optimization Variables", "createOptimization.lowerLimit": "Lower Limit", "createOptimization.upperLimit": "Upper Limit", "createOptimization.stepSize": "Step Size", "createOptimization.objectiveFunction": "Objective function", "createOptimization.optimizationAlgorithm": "Optimization Algorithm", "createOptimization.optimizationObjective": "Optimization Objective", "createOptimization.optimizationTarget": "Optimization Target", "createOptimization.anodeWeightValidation": "Anode weight optimization is not allowed. Only blends containing Group 14 SCC55 material are supported.", "createOptimization.cathodeWeightValidation": "Cathode weight optimization is not allowed. Please use one of the following blends: BASF_NCM90_PC_BT98B (material 1) with either Mitrachem_LMFP (material 2) or LFP400_IBUtec (material 2)", "createOptimization.cellCostValidation": "No cell cost available. Please set all prices in the BOM editor.", "createOptimization.lowerLimitValidation": "Lower limit must be less than the upper limit", "createOptimization.stepSizeValidation": "Step size must be smaller than the difference between upper and lower limits", "createOptimization.swellingValidation": "Optimization of the swelling objective is not possible. Please enable swelling.", "createOptimization.advancedSettings": "Advanced Settings", "createOptimization.numInitialPoints": "Number of initial points", "createOptimization.numIterations": "Number of iterations", "createOptimization.warningMessageGrid": "This optimization configuration may take longer to complete. To speed it up, consider narrowing the variable limits (lower and upper) or increasing the step size.", "createOptimization.warningMessageBayesian": "This optimization configuration may take longer to complete. To speed it up, try reducing the number of initial points or decreasing the number of iterations.", "electrodePair.aluminumFoilThickness.input.label": "Aluminum foil thickness", "electrodePair.anodeDensity.input.label": "Anode density", "electrodePair.anodeThickness.label": "Anode thickness", "electrodePair.balancingAnodeCathode.label": "Balancing (anode/cathode)", "electrodePair.calendarDates.label": "Calendar dates", "electrodePair.cathodeDensity.input.label": "Cathode density", "electrodePair.cathodeThickness.label": "Cathode thickness", "electrodePair.cellLayerThickness.label": "Cell layer thickness", "electrodePair.chargeDensityAnode.label": "Charge density anode", "electrodePair.chargeDensityCathode.input.label": "Charge density cathode ", "electrodePair.coatingAreaLayerThickness.label": "Coating areal density and layer thickness", "electrodePair.coatingThicknessAnode.label": "Coating thickness anode", "electrodePair.coatingThicknessCathode.label": "Coating thickness cathode", "electrodePair.copperFoilThickness.input.label": "Copper foil thickness", "electrodePair.electrodeThickness.label": "Electrode thickness", "electrodePair.loadingAnode.label": "Loading anode", "electrodePair.loadingCathode.label": "Loading cathode", "electrodePair.proposityAnode.label": "Proposity anode", "electrodePair.proposityCathode.label": "Proposity cathode", "electrodePair.separatorThickness.input.label": "Separator thickness", "electrolyte.activeSurfaceAnode": "Active surface anode", "electrolyte.electrolyteConsumptionThroughSeiFormation": "Electrolyte consumption through SEI formation", "electrolyte.estimatedFromSeiReaction": "Estimated from SEI-reaction", "electrolyte.firstCycleEfficiency": "First Cycle Efficiency", "electrolyte.seiGrowthPerCharge": "SEI growth per charge", "electrolyte.suggestedAmountOfElectrolyte": "Suggested amount of electrolyte (with buffer for formation)", "electrolyte.warning.message": "First estimate of electrolyte consumption by SEI reaction", "electrolyte.warning.request": "We need additional input here!", "electrolyte.warning.source": "(Source: {$INTERPOLATION})", "energyContent.capacityAndEnergyContent": "Capacity and energy content", "energyContent.cellVolumeOverall.label": "Volume of whole cell", "energyContent.cellWeightOverall.label": "Weight of whole cell", "energyContent.energyDensityGravimetric": "Energy density gravimetric", "energyContent.energyDensityVolumentric": "Energy density volumetric", "energyContent.nominalVoltage.label": "Nominal voltage", "energyContent.parallelCellsCount.label": "Parallel cells", "energyContent.priceCell.label": "Material price per cell", "energyContent.serialCellsCount.label": "Serial cells", "energyContent.cellsPerModule.label": "Cells per module", "materials.activeMaterialWeight.input.label": "Active Material", "materials.aluminiumDensity": "Aluminium Density", "materials.anode.fullCellUtilization": "Anode Full Cell Utilization", "materials.anode.halfCellUtilization": "Anode Half Cell Utilization", "materials.binderWeight.input.label": "Binder", "materials.cathode.fullCellUtilization": "Cathode Full Cell Utilization", "materials.cathode.halfCellUtilization": "Cathode Half Cell Utilization", "materials.conductiveAdditive.input.label": "Conductive Additive Materials", "materials.copperDensity": "Copper Density", "materials.currentCollector.input.label": "Current Collector", "materials.electrolyte.input.label": "Elektrolyt", "materials.electrolyteDensity": "Electrolyte Density", "materials.group14.label": "Group 14", "materials.lithiumWeight.label": "Lithium", "materials.nanoscale.label": "Nanoscale", "materials.percentages.error": "Total of percentages must equal 100", "materials.prelithiationProcess.label": "Prelithiation Process", "materials.separator.input.label": "Separator", "materials.separatorDensity": "Separator Density", "materials.separatorPorousness": "Separator <PERSON>", "materials.weightAfterPrelithiation.header": "Weight ratio after prelithiation", "parameterOptimization.afterOptimization": "After Optimization", "parameterOptimization.beforeOptimization": "Before Optimization", "parameterOptimization.cellKPI": "Cell KPI", "parameterOptimization.efficiencyGain": "Efficiency Gain", "parameterOptimization.chartType": "Chart Type", "parameterOptimization.noOptimizationMessage": "Currently no optimization available for this cell design.", "parameterOptimization.optimizationFailed": "Your optimization has failed. Please create new.", "parameterOptimization.optimizationOutcome": "Optimization Outcome", "parameterOptimization.optimizationRunning": "Your optimization process is in progress. Once it is completed the results will be shown here. Please wait.", "parameterOptimization.optimizationSettings": "Optimization Settings", "parameterOptimization.optimizationVariable": "Optimization Variable", "parameterOptimization.timeRemaining": "Estimated Remaining Time: {$INTERPOLATION}", "parameterOptimization.loading": "Loading", "swelling.assumptions": "Assumptions", "swelling.cf3Absolut": "CF3 Absolute Breathing with Compression", "swelling.cf3compressed": "CF3 Breathing with Compression", "swelling.cf3uncompressed": "CF3 uncompressed Breathing", "swelling.compressibility": "Compressibility over pressure range", "swelling.constant": "Swelling Parameter {$INTERPOLATION}", "swelling.constantPoreVolume": "Constant pore volume", "swelling.expansionOfSCC": "Expansion of SCC according to dilatometry: a*SoL^b", "swelling.fieldIsRequired": "Field is required.", "swelling.freeSpace": "Free space after formation", "swelling.layerFreeSpace": "Layer Free Space", "swelling.noCathode": "No cathode expansion/shrinking", "swelling.parameters": "Swelling Parameters", "swelling.stackBreathing": "Stack Breathing", "swelling.sol": "SoL_SCC=SoL_Anode at SoC=100%", "swelling.swellingCalculation": "Swelling Calculation", "swelling.swellingCalculations": "Swelling Calculations", "swelling.swellingDuringFormation": "Swelling during formation", "swelling.totalBreathing": "Total breathing per layer", "swelling.wrongFormatAndMaterial": "Swelling calculation is unavailable because the cell format is not CF3 and the anode material is incorrect. Please use CF3 format and ensure that the anode material is Group 14 SCC.", "swelling.wrongFormat": "Swelling calculation is only available for CF3 cell format. Please update the cell format to enable calculations.", "swelling.wrongMaterial": "Swelling calculation requires the anode material to be Group 14 SCC. Please select Group 14 SCC as the anode material.", "swelling.wrongMaterialBlend": "Swelling calculation requires at least one anode material to be Group 14 SCC in a blend. Please ensure at least one of the selected materials is Group 14 SCC.", "tab.anode": "Anode", "tab.balancing": "Balancing", "tab.bom": "BOM", "tab.cathode": "Cathode", "tab.cell": "Cell", "tab.cellDesign": "Cell Design", "tab.cellSpecs": "Cell Specs", "tab.chemicalDesign": "Chemical Design", "tab.electrodePair": "Electrode Pair", "tab.electrolyte": "Electrolyte", "tab.designAnalysis": "Design Analysis", "tab.energyContent??": "Energy Content", "tab.materials": "Materials", "tab.socTool": "SOC Tool", "tab.thermalProjection": "Thermal Projection", "tab.cellOptimization": "Cell Optimization", "tab.createOptimization": "Create Optimization", "tab.parameterOptimization": "Parameter Optimization", "toast.optimizationCreated": "Optimized design with name '{$INTERPOLATION}' created succesfully!", "toast.optimizationCompletedDesc": "Cell design is updated.", "toast.optimizationCompletedTitle": "Optimization process is now completed", "toast.optimizationRunningDesc": "Optimization is not yet completed. Please wait for optimization process to complete.", "toast.optimizationRunningTitle": "Optimization process is still running", "createTolerance.histogramResults": "Distribution Analysis", "createTolerance.histogramMetric": "Select Metric", "createTolerance.chartTitle": "Distribution of Values", "createTolerance.chartXAxis": "Value Range", "createTolerance.chartYAxis": "Frequency", "createTolerance.binCount": "Number of Bins", "createTolerance.binCountValue": "{$INTERPOLATION} Bins"}, "primeng": {"startsWith": "Starts with", "contains": "Contains", "notContains": "Not contains", "endsWith": "Ends with", "equals": "Equals", "notEquals": "Not equals", "noFilter": "No Filter", "lt": "Less than", "lte": "Less than or equal to", "gt": "Greater than", "gte": "Greater than or equal to", "is": "Is", "isNot": "Is not", "before": "Before", "after": "After", "dateIs": "Date is", "dateIsNot": "Date is not", "dateBefore": "Date is before", "dateAfter": "Date is after", "clear": "Clear", "apply": "Apply", "matchAll": "Match All", "matchAny": "Match Any", "addRule": "Add Rule", "removeRule": "Remove Rule", "accept": "Yes", "reject": "No", "choose": "<PERSON><PERSON>", "upload": "Upload", "cancel": "Cancel", "dayNames": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "dayNamesShort": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "dayNamesMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"], "monthNames": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "monthNamesShort": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "dateFormat": "dd <PERSON> yy", "firstDayOfWeek": 1, "today": "Today", "weekHeader": "Wk", "weak": "Weak", "medium": "Medium", "strong": "Strong", "passwordPrompt": "Enter a password", "emptyMessage": "No results found", "emptyFilterMessage": "No results found"}}
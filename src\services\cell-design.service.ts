import { HttpClient, HttpContext, HttpResponse, HttpStatusCode } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, catchError, map, of, tap, throwError } from 'rxjs';

import { UNHANDLED_ERROR_CODES } from '@com/app/shared/error-interceptor';
import { ConfigService } from './config.service';
import {
    CellFormatCylinderProperties,
    CellFormatPouchProperties,
    CellFormatPrismaProperties,
    CellFormatProperties,
} from './cell-format.service';
import { ToleranceSettings, ToleranceValueTypeEnum } from './tolerance.service';

export interface BusinessCase {
    _id: string;
    label: string;
}

export interface CellDesignMetadata {
    name: string;
    projectName: string;
    projectState?: string;
    partNumber?: string;
    description?: string;
}

export interface CellDesign extends CellDesignMetadata {
    // Cathode
    cathodeMaterials: MaterialWeight[];
    cathodeQAim: number | null;
    cathodeQAimFirstCharge: number | null;
    cathodeFixedURange?: number[];

    // Anode
    anodeMaterials: MaterialWeight[];
    anodeQAim: number | null;
    anodeQAimFirstCharge: number | null;
    anodeFixedURange?: number[];
    prelithiationCapacity: number | null;

    // Balancing
    balancingUMin: number;
    balancingUMax: number;
    balancingNpRatioFirst: number;

    // Materials
    activeMaterialAnodeWeightPercent: number;
    activeMaterialCathodeWeightPercent: number;
    anodeBinderMaterials: MaterialMetricsModel[];
    cathodeBinderMaterials: MaterialMetricsModel[];
    anodeConductiveAdditiveMaterials: MaterialMetricsModel[];
    cathodeConductiveAdditiveMaterials: MaterialMetricsModel[];

    prelithiationProcess: 'nanoscale' | 'group14';

    electrolyteMaterialId: string;
    separatorMaterialId: string;
    aluminiumCurrentCollectorMaterialId: string;
    copperCurrentCollectorMaterialId: string;

    // Electrode Pair View
    cathodeCalanderDensity: number;
    anodeCalanderDensity: number;
    cathodeArealCapacity: number;
    thicknessAluminiumFoil: number;
    thicknessCopperFoil: number;
    thicknessSeparator: number;

    // Cell View
    cellFormatId: string;
    cellFormatIsDefault: boolean;
    cellFormatProperties: CellFormatProperties | null;
    electrolyteSwelling: number;

    // Electrolyte View
    // between 0 - 3
    electrolytAmount: number;
    agingTableEditValues: { [key: string]: number };

    // Energy Content View
    safety: number;
    parallelCellsCount: number;
    serialCellsCount: number;
    moduleCount: number;

    // BOM View
    scrap: number;
    businessCaseId: string;
    bomPrices: { [key: string]: number };

    // Optimization
    optimizationId: string | null;

    // Cell Swelling
    swellingDuringFormation: number;
    compressibilityOverPressureRange: number;

    // Tolerance Analysis
    toleranceSettings?: ToleranceSettings;
    // this list is going to be extended for further views
}

export interface CellDesignPouch extends CellDesign {
    cellFormatProperties: CellFormatPouchProperties;
}

export interface CellDesignPrisma extends CellDesign {
    cellFormatProperties: CellFormatPrismaProperties;
}

export interface CellDesignCylinder extends CellDesign {
    cellFormatProperties: CellFormatCylinderProperties;
}

export interface UserModel {
    _id: string;
    oid: string;
    email: string;
    name: string;
}

export interface Id {
    _id: string;
}

export interface CellDesignWithId extends Id, CellDesign {
    designId: string;
    createdAt: string;
    createdBy: UserModel;
    modifiedAt: string;
    modifiedBy: UserModel;
    deletedAt?: string | null;
    deletedBy?: UserModel | null;
    released: boolean;
    releasedBy?: UserModel | null;
    releasedAt?: string;

    // The app version used to save the format.
    // Follows the semver convention.
    version: string;
}

export interface MaterialWeight {
    materialId: string;
    materialVersionId?: string | null;
    weightPercent: number;
}

export interface MaterialMetricsModel {
    materialId: string;
    weightPercent: number;
    density: number;
}

export type DesignResponseOk = {
    status: HttpStatusCode.Ok;
    design: CellDesignWithId;
};

export type DesignResponseConflict = {
    status: HttpStatusCode.Conflict;
};

export type DesignResponseAlreadyExists = {
    status: HttpStatusCode.SeeOther;
    newId: string;
};

export type CreateDesignResponse = DesignResponseOk | DesignResponseAlreadyExists;

export type UpdateDesignResponse = DesignResponseOk | DesignResponseConflict;

const defaultCellDesign: CellDesign = {
    name: '',
    projectName: '',
    projectState: '',
    cathodeMaterials: [{ materialId: 'basf_ncm83_pc', materialVersionId: '0', weightPercent: 100 }],
    // cathodeDates: null,
    cathodeQAim: null,
    cathodeQAimFirstCharge: null,
    cathodeFixedURange: [3, 4.3],
    anodeMaterials: [{ materialId: 'g14_btr_blend_70_247', materialVersionId: '0', weightPercent: 100 }],
    //anodeDates: null,
    anodeQAim: null,
    anodeQAimFirstCharge: null,
    anodeFixedURange: [0.005, 1.2],
    prelithiationCapacity: null,
    balancingNpRatioFirst: 1.1,
    balancingUMax: 4.24,
    balancingUMin: 2.5,

    activeMaterialAnodeWeightPercent: 94.7,

    anodeBinderMaterials: [{ materialId: 'binder_paa_aquacharge', weightPercent: 4, density: 1 }],
    anodeConductiveAdditiveMaterials: [
        { materialId: 'leitruss_c65', weightPercent: 1.25, density: 2 },
        { materialId: 'cnt', weightPercent: 0.05, density: 3 },
    ],

    prelithiationProcess: 'nanoscale',

    electrolyteMaterialId: 'default_electrolyte',
    separatorMaterialId: 'separator',
    aluminiumCurrentCollectorMaterialId: 'aluminium',
    copperCurrentCollectorMaterialId: 'copper',

    activeMaterialCathodeWeightPercent: 96,

    cathodeBinderMaterials: [{ materialId: 'binder_5130_solef', weightPercent: 2.0, density: 1.77 }],
    cathodeConductiveAdditiveMaterials: [
        { materialId: 'leitruss_c65', weightPercent: 2.0, density: 1.6 },
        { materialId: 'cnt', weightPercent: 0, density: 1.6 },
    ],

    // Electrode Pair View
    cathodeCalanderDensity: 3.5,
    anodeCalanderDensity: 1,
    cathodeArealCapacity: 4,
    thicknessAluminiumFoil: 12,
    thicknessCopperFoil: 8,
    thicknessSeparator: 18,

    cellFormatId: 'cf1_thin',
    cellFormatIsDefault: true,
    cellFormatProperties: {
        cellLength: 354,
        cellThickness: 10.72,
        cellWidth: 102,
        coatingLengthAnode: 318,
        coatingLengthCathode: 316,
        coatingLengthSeparator: 322,
        coatingWidthAnode: 95,
        coatingWidthCathode: 94,
        coatingWidthSeparator: 97,
        housingThickness: 0.183,
        housingWeight: null,
        swellingBuffer: 0,
    },
    electrolyteSwelling: 3,
    electrolytAmount: 1.4,
    agingTableEditValues: {},
    safety: 0.95,
    parallelCellsCount: 2,
    serialCellsCount: 198,
    moduleCount: 1,

    scrap: 1,
    businessCaseId: '100 MWh',
    bomPrices: {},
    optimizationId: null,
    swellingDuringFormation: 11,
    compressibilityOverPressureRange: 3,
    toleranceSettings: { variables: [], valueType: ToleranceValueTypeEnum.absolute },
};
@Injectable({
    providedIn: 'root',
})
export class CellDesignService {
    private _bomBusinessCases: BusinessCase[] | undefined = undefined;

    private _currentDesign = new BehaviorSubject<CellDesign | null>(null);

    public constructor(private _config: ConfigService, private _http: HttpClient) {}

    public getInitialCellDesign(): Observable<CellDesign> {
        this._currentDesign.next(defaultCellDesign);

        return of(defaultCellDesign);
    }

    public getAllCellDesigns(): Observable<CellDesignWithId[]> {
        return this._http.get<CellDesignWithId[]>(`${this._config.data.baseUrl}/api/cell-design/`);
    }

    public getSingleCellDesign(cellDesignId: string): Observable<CellDesignWithId> {
        return this._http.get<CellDesignWithId>(`${this._config.data.baseUrl}/api/cell-design/${cellDesignId}`);
    }

    public createCellDesign(cellDesign: CellDesign): Observable<CreateDesignResponse> {
        const context = new HttpContext().set(UNHANDLED_ERROR_CODES, [HttpStatusCode.SeeOther]);

        return this._http
            .post<CellDesignWithId>(`${this._config.data.baseUrl}/api/cell-design/`, cellDesign, {
                observe: 'response',
                context,
            })
            .pipe(
                map(
                    (response: HttpResponse<CellDesignWithId>) =>
                        ({
                            design: response.body,
                            status: response.status,
                        } as DesignResponseOk)
                ),
                catchError((response, _caught) => {
                    if (response.status === HttpStatusCode.SeeOther) {
                        return of<DesignResponseAlreadyExists>({
                            status: response.status,
                            newId: response.error._id,
                        });
                    }

                    return throwError(() => response);
                })
            );
    }

    public updateCellDesign({
        createdAt: _a,
        createdBy: _b,
        releasedAt: _c,
        releasedBy: _d,
        modifiedAt: _e,
        modifiedBy: _f,
        ...cellDesign
    }: CellDesignWithId): Observable<UpdateDesignResponse> {
        const context = new HttpContext().set(UNHANDLED_ERROR_CODES, [HttpStatusCode.Conflict]);

        return this._http
            .put<CellDesignWithId>(`${this._config.data.baseUrl}/api/cell-design/${cellDesign._id}`, cellDesign, {
                context,
            })
            .pipe(
                map((design) => ({ design: design, status: HttpStatusCode.Ok } as DesignResponseOk)),
                catchError((response, _caught) => {
                    if (response.status === HttpStatusCode.Conflict) {
                        return of<DesignResponseConflict>({ status: response.status });
                    }

                    return throwError(() => response);
                })
            );
    }

    public deleteCellDesign(cellDesignId: string): Observable<boolean> {
        return this._http.delete<boolean>(`${this._config.data.baseUrl}/api/cell-design/${cellDesignId}`);
    }

    public getBusinessCase(): Observable<BusinessCase[]> {
        if (this._bomBusinessCases !== undefined) {
            return of(this._bomBusinessCases);
        } else {
            return this._http
                .get<BusinessCase[]>(`${this._config.data.baseUrl}/api/business-case/`)
                .pipe(tap((value) => (this._bomBusinessCases = value)));
        }
    }

    public getCurrentDesign(): Observable<CellDesign | null> {
        return this._currentDesign;
    }

    public setCurrentDesign(design: CellDesign): void {
        this._currentDesign.next(design);
    }
}

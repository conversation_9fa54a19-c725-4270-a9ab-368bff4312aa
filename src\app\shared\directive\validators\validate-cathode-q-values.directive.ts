import { Directive } from '@angular/core';
import { FormGroup, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';

export const validateCathodeQValuesMinimumError = 'ValidateCathodeQValuesMinimumError';
export const validateCathodeQValuesComparisonError = 'ValidateCathodeQValuesComparisonError';

@Directive({
    selector: '[comValidateCathodeQValuesDirective]',
    providers: [{ provide: NG_VALIDATORS, useExisting: ValidateCathodeQValuesDirective, multi: true }],
})
export class ValidateCathodeQValuesDirective implements Validator {
    private readonly minimumValue = 150;

    public validate(group: FormGroup): ValidationErrors | null {
        const cathodeQAimControl = group.get('cathode_q_aim');
        const cathodeQAimFirstChargeControl = group.get('cathode_q_aim_1');

        if (!cathodeQAimControl || !cathodeQAimFirstChargeControl) {
            return null;
        }

        const cathodeQAim = cathodeQAimControl.value;
        const cathodeQAimFirstCharge = cathodeQAimFirstChargeControl.value;
        const errors: ValidationErrors = {};

        // Check minimum value validation for both fields
        if (cathodeQAim !== null && cathodeQAim !== undefined && cathodeQAim < this.minimumValue) {
            errors[validateCathodeQValuesMinimumError] = { field: 'cathodeQAim', value: cathodeQAim };
        }

        if (cathodeQAimFirstCharge !== null && cathodeQAimFirstCharge !== undefined && cathodeQAimFirstCharge < this.minimumValue) {
            errors[validateCathodeQValuesMinimumError] = { field: 'cathodeQAimFirstCharge', value: cathodeQAimFirstCharge };
        }

        // Check comparison validation: Q1st must be > Qrev
        if (
            cathodeQAim !== null && cathodeQAim !== undefined &&
            cathodeQAimFirstCharge !== null && cathodeQAimFirstCharge !== undefined &&
            cathodeQAimFirstCharge <= cathodeQAim
        ) {
            errors[validateCathodeQValuesComparisonError] = { 
                qrev: cathodeQAim, 
                q1st: cathodeQAimFirstCharge 
            };
        }

        return Object.keys(errors).length > 0 ? errors : null;
    }
}

<div *ngIf="!design || !selectedToleranceSettings" class="p-4">
    <span i18n="@@createTolerance.noToleranceMessage">Toleranzanalyse nicht verfügbar.</span>
</div>

<div class="grid align-items-stretch">
    <div *ngIf="design && selectedToleranceSettings" class="col-12 xl:col-8 pb-5">
        <p-panel class="mb-4">
            <ng-template pTemplate="header">
                <strong i18n="@@createTolerance.parameterSet" class="text-xl">Parametersatz</strong>
            </ng-template>

            <div class="grid align-items-center mb-4">
                <label i18n="@@createTolerance.valueType" for="valueType" class="col-12 sm:col-3 lg:col-2"
                    >Wertetyp</label
                >
                <p-dropdown
                    name="valueType"
                    [inputId]="'valueType'"
                    [options]="valueTypeOptions"
                    [(ngModel)]="selectedToleranceSettings.valueType"
                    (ngModelChange)="onValueTypeChange()"
                    optionLabel="name"
                    optionValue="value"
                    class="col-12 sm:col-9 lg:col-4 p-fluid"
                ></p-dropdown>
            </div>

            <div class="grid">
                <span i18n="@@createTolerance.toleranceVariables" class="col-6">Toleranzvariablen</span>
                <label i18n="@@createTolerance.minimumValue" for="minimumValue" class="col-2">Minimum Wert</label>
                <label i18n="@@createTolerance.standardValue" for="standardValue" class="col-2">Standard Wert</label>
                <label i18n="@@createTolerance.maximumValue" for="maximumValue" class="col-2">Maximum Wert</label>

                <ng-container *ngFor="let variable of selectedToleranceSettings.variables; let i = index">
                    <div class="col-6 flex">
                        <p-button
                            icon="pi pi-minus"
                            (onClick)="removeToleranceVariable(i)"
                            class="mr-0 lg:mr-2"
                        ></p-button>
                        <div class="col-11 p-fluid pt-0">
                            <p-dropdown
                                [name]="'toleranceVariable' + i"
                                [inputId]="'toleranceVariable' + i"
                                [options]="toleranceVariablesOptions"
                                [(ngModel)]="selectedToleranceSettings.variables[i].id"
                                (ngModelChange)="onVariableChange($event, i)"
                                optionLabel="name"
                                optionValue="id"
                                [ngClass]="{
                                    'ng-invalid': toleranceVariablesValidationErrors.get(variable.id)
                                        ?.missingStandardValue
                                }"
                            ></p-dropdown>
                            <small
                                i18n="@@createTolerance.missingStandardValue"
                                *ngIf="toleranceVariablesValidationErrors.get(variable.id)?.missingStandardValue"
                                class="p-error"
                            >
                                Der Wert für diese Toleranzvariable ist im Zelldesign nicht angegeben. Um die Toleranz
                                zu berechnen, müssen Sie diesen Wert zuerst festlegen.
                            </small>
                        </div>
                    </div>

                    <div class="col-2 p-fluid">
                        <p-inputNumber
                            comCustomizeInput
                            [inputId]="'minimumValue' + i"
                            [name]="'minimumValue' + i"
                            [(ngModel)]="variable.lowerLimit"
                            (ngModelChange)="handleToleranceVariableChange(variable)"
                            mode="decimal"
                            [prefix]="
                                selectedToleranceSettings.valueType === ToleranceValueTypeEnum.absolute ? '' : '- '
                            "
                            [suffix]="
                                selectedToleranceSettings.valueType === ToleranceValueTypeEnum.relativePercentage
                                    ? ' %'
                                    : selectedToleranceSettings.variables[i].unit
                                    ? ' ' + selectedToleranceSettings.variables[i].unit
                                    : ''
                            "
                            [required]="true"
                            [ngClass]="{
                                'ng-invalid': toleranceVariablesValidationErrors.get(variable.id)?.missingMinValue
                            }"
                        ></p-inputNumber>
                        <small
                            i18n="@@swelling.fieldIsRequired"
                            *ngIf="toleranceVariablesValidationErrors.get(variable.id)?.missingMinValue"
                            class="p-error"
                        >
                            Feld ist erforderlich
                        </small>
                    </div>

                    <div class="col-2 p-fluid">
                        <input
                            pInputText
                            [id]="'standardValue' + i"
                            [name]="'standardValue' + i"
                            [ngModel]="standardValuesWithUnit.get(selectedToleranceSettings.variables[i].id)"
                            [disabled]="true"
                            [ngClass]="{
                                'ng-invalid': toleranceVariablesValidationErrors.get(variable.id)?.missingStandardValue
                            }"
                        />
                    </div>

                    <div class="col-2 p-fluid">
                        <p-inputNumber
                            comCustomizeInput
                            [inputId]="'maximumValue' + i"
                            [name]="'maximumValue' + i"
                            [(ngModel)]="selectedToleranceSettings.variables[i].upperLimit"
                            (ngModelChange)="handleToleranceVariableChange(variable)"
                            mode="decimal"
                            [prefix]="
                                selectedToleranceSettings.valueType === ToleranceValueTypeEnum.absolute ? '' : '+ '
                            "
                            [suffix]="
                                selectedToleranceSettings.valueType === ToleranceValueTypeEnum.relativePercentage
                                    ? ' %'
                                    : selectedToleranceSettings.variables[i].unit
                                    ? ' ' + selectedToleranceSettings.variables[i].unit
                                    : ''
                            "
                            [required]="true"
                            [ngClass]="{
                                'ng-invalid': toleranceVariablesValidationErrors.get(variable.id)?.missingMaxValue
                            }"
                        ></p-inputNumber>
                        <small
                            i18n="@@swelling.fieldIsRequired"
                            *ngIf="toleranceVariablesValidationErrors.get(variable.id)?.missingMaxValue"
                            class="p-error"
                        >
                            Feld ist erforderlich
                        </small>
                    </div>
                </ng-container>
            </div>
            <p-button
                icon="pi pi-plus"
                (onClick)="addToleranceVariable()"
                [disabled]="selectedToleranceSettings.variables.length >= toleranceVariablesOptions.length"
            ></p-button>
        </p-panel>

        <div class="flex align-items-center gap-3 mb-4 mt-4">
            <p-button
                i18n-label="@@createTolerance.calculateTolerance"
                label="Toleranz berechnen"
                (click)="calculateTolerance()"
                [disabled]="toleranceSettingsInvalid || calculatingTolerance"
                class="pl-3"
            ></p-button>

            <p-progressSpinner *ngIf="calculatingTolerance" styleClass="w-2rem h-2rem"></p-progressSpinner>

            <span *ngIf="calculatingTolerance" i18n="@@createTolerance.calculating">Toleranz wird berechnet...</span>
        </div>

        <!-- Variable Results Panel -->
        <p-panel *ngIf="variableResults" class="mt-4 mb-4">
            <ng-template pTemplate="header">
                <strong i18n="@@createTolerance.variableResults" class="text-xl">Variablenergebnisse</strong>
            </ng-template>

            <div *ngIf="hasMultipleVariables(fullToleranceResults)" class="grid align-items-center mb-4">
                <label i18n="@@createTolerance.variableCategory" for="variableCategory" class="col-12 sm:col-3 lg:col-2"
                    >Variablenkategorie</label
                >
                <p-dropdown
                    name="variableCategory"
                    [inputId]="'variableCategory'"
                    [options]="variableCategoryOptions"
                    [(ngModel)]="selectedVariableCategory"
                    (ngModelChange)="onVariableCategoryChange()"
                    optionLabel="name"
                    optionValue="value"
                    class="col-12 sm:col-9 lg:col-4 p-fluid"
                ></p-dropdown>
            </div>

            <div *ngIf="variableResults" class="mb-3">
                <h3 class="text-lg font-medium">
                    <span i18n="@@createTolerance.resultsFor">Ergebnisse für:</span> {{ getSelectedVariableName() }}
                </h3>
            </div>

            <div *ngIf="variableResults" class="grid mb-4">
                <span i18n="@@parameterOptimization.cellKPI" class="col-6">Zell-KPI</span>
                <label i18n="@@createOptimization.lowerLimit" class="col-2 text-center">Unteres Limit</label>
                <label i18n="@@createTolerance.standardValueResult" class="col-2 text-center">Standard Value</label>
                <label i18n="@@createOptimization.upperLimit" class="col-2 text-center">Oberes Limit</label>

                <ng-container *ngFor="let result of variableResults">
                    <div class="col-6 flex">
                        <span>{{ result.name }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span *ngIf="result.minValue !== null">
                            {{ result.minValue | numberTwoFractionDigits }}{{ result.unit ? " " + result.unit : "" }}
                        </span>
                        <span *ngIf="result.minValue === null">{{ notAvailable }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span *ngIf="result.standardValue !== null">
                            {{ result.standardValue | numberTwoFractionDigits
                            }}{{ result.unit ? " " + result.unit : "" }}
                        </span>
                        <span *ngIf="result.standardValue === null">{{ notAvailable }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span *ngIf="result.maxValue !== null">
                            {{ result.maxValue | numberTwoFractionDigits }}{{ result.unit ? " " + result.unit : "" }}
                        </span>
                        <span *ngIf="result.maxValue === null">{{ notAvailable }}</span>
                    </div>
                </ng-container>
            </div>
        </p-panel>

        <!-- Chained Results Panel -->
        <p-panel *ngIf="chainedResults && hasMultipleVariables(fullToleranceResults)" class="mt-4">
            <ng-template pTemplate="header">
                <strong i18n="@@createTolerance.chainedResults" class="text-xl">Verkettete Ergebnisse</strong>
            </ng-template>

            <div class="grid mb-4">
                <span i18n="@@parameterOptimization.cellKPI" class="col-6">Zell-KPI</span>
                <label i18n="@@createTolerance.minimumValue" class="col-2 text-center">Minimum Wert</label>
                <label i18n="@@createTolerance.standardValue" class="col-2 text-center">Standard Wert</label>
                <label i18n="@@createTolerance.maximumValue" class="col-2 text-center">Maximum Wert</label>

                <ng-container *ngFor="let result of chainedResults">
                    <div class="col-6 flex">
                        <span>{{ result.name }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span *ngIf="result.minValue !== null">
                            {{ result.minValue | numberTwoFractionDigits }}{{ result.unit ? " " + result.unit : "" }}
                        </span>
                        <span *ngIf="result.minValue === null">{{ notAvailable }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span
                            >{{ result.standardValue | numberTwoFractionDigits
                            }}{{ result.unit ? " " + result.unit : "" }}</span
                        >
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span *ngIf="result.maxValue !== null">
                            {{ result.maxValue | numberTwoFractionDigits }}{{ result.unit ? " " + result.unit : "" }}
                        </span>
                        <span *ngIf="result.maxValue === null">{{ notAvailable }}</span>
                    </div>
                </ng-container>
            </div>
        </p-panel>

        <!-- Histogram Chart Panel -->
        <p-panel *ngIf="hasMultipleVariables(fullToleranceResults) && histogramMetricOptions.length > 0" class="mt-4">
            <ng-template pTemplate="header">
                <strong i18n="@@createTolerance.histogramResults" class="text-xl">Verteilungsanalyse</strong>
            </ng-template>

            <div class="grid align-items-center mb-4">
                <label i18n="@@createTolerance.histogramMetric" for="histogramMetric" class="col-12 sm:col-3 lg:col-2"
                    >Metrik auswählen</label
                >
                <p-dropdown
                    name="histogramMetric"
                    [inputId]="'histogramMetric'"
                    [options]="histogramMetricOptions"
                    [(ngModel)]="selectedHistogramMetric"
                    (ngModelChange)="onHistogramMetricChange()"
                    optionLabel="name"
                    optionValue="value"
                    class="col-12 sm:col-9 lg:col-4 p-fluid"
                ></p-dropdown>
            </div>

            <div *ngIf="histogramData" class="grid align-items-center mb-4">
                <label i18n="@@createTolerance.binCount" for="binCount" class="col-12 sm:col-3 lg:col-2"
                    >Anzahl der Bins</label
                >
                <div class="col-12 sm:col-9 lg:col-4">
                    <p-slider
                        [(ngModel)]="binCount"
                        [min]="minBinCount"
                        [max]="maxBinCount"
                        (onChange)="updateHistogramChart()"
                        [step]="1"
                        class="w-full"
                    ></p-slider>
                    <small class="block mt-2" i18n="@@createTolerance.binCountValue">{{ binCount }} Bins</small>
                </div>
            </div>

            <div *ngIf="histogramData" class="mb-4">
                <p-chart
                    #histogramChart
                    type="bar"
                    [data]="histogramData"
                    [options]="histogramOptions"
                    height="300px"
                ></p-chart>
            </div>
        </p-panel>
    </div>
</div>

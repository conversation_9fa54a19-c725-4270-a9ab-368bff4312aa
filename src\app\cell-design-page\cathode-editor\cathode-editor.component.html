<div *ngIf="materials && blendableMaterials && design" class="grid align-items-stretch">
    <div class="col-12 lg:col-6">
        <p-card>
            <ng-container
                [ngModelGroup]="formGroupNames.cathodeMaterial"
                #cathodeMaterialFormGroup="ngModelGroup"
                comValidateGroupSumDirective
                [desiredSum]="desiredSum"
                [includedControlNames]="[controlNames.cathodeMaterial1Weight, controlNames.cathodeMaterial2Weight]"
                [skipValidation]="true"
            >
                <div class="field grid">
                    <label
                        for="cathode_developer_mode"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.common.developerMode.checkbox.label"
                    >
                        Entwicklermodus
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-inputSwitch
                            name="cathode_developer_mode"
                            inputId="cathode_developer_mode"
                            [ngModel]="design.cathodeMaterials.length > 1"
                            (ngModelChange)="toggleDeveloperMode($event)"
                        ></p-inputSwitch>
                    </div>
                </div>

                <div class="field grid">
                    <label
                        for="cathode_material1_id"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.common.material.input.label"
                    >
                        Material {{ 1 }}
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-dropdown
                            name="cathode_material1_id"
                            inputId="cathode_material1_id"
                            [options]="design.cathodeMaterials.length > 1 ? blendableMaterials : materials"
                            [(ngModel)]="design.cathodeMaterials[0].materialId"
                            optionLabel="name"
                            optionValue="id"
                        ></p-dropdown>
                    </div>
                </div>

                <div
                    class="field grid"
                    [class.hidden]="!materialMap.get(design.cathodeMaterials[0].materialId)?.length"
                >
                    <label
                        for="cathode_material1_version_id"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.common.material.version.input.label"
                    >
                        Version Material {{ 1 }}
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-dropdown
                            name="cathode_material1_version_id"
                            inputId="cathode_material1_version_id"
                            [options]="materialMap.get(design.cathodeMaterials[0].materialId)!"
                            [(ngModel)]="design.cathodeMaterials[0].materialVersionId"
                            optionLabel="description"
                            optionValue="id"
                            [disabled]="materialMap.get(design.cathodeMaterials[0].materialId)?.length === 1"
                        >
                            <ng-template let-selectedVersion pTemplate="selectedItem">
                                <div>{{ selectedVersion.description }} - {{ selectedVersion.date | date }}</div>
                            </ng-template>
                            <ng-template let-version pTemplate="item">
                                <div>{{ version.description }} - {{ version.date | date }}</div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>

                <ng-container *ngIf="design.cathodeMaterials.length > 1">
                    <div class="field grid">
                        <label
                            [for]="controlNames.cathodeMaterial1Weight"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.ratio.input.label"
                        >
                            Anteil Material {{ 1 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.cathodeMaterial1Weight"
                                [inputId]="controlNames.cathodeMaterial1Weight"
                                [(ngModel)]="design.cathodeMaterials[0].weightPercent"
                                [maxFractionDigits]="2"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [max]="100"
                                [required]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeMaterialFormGroup.invalid
                                }"
                            ></p-inputNumber>
                        </div>
                        <div
                            *ngIf="cathodeMaterialFormGroup.invalid && cathodeMaterialFormGroup.errors?.[groupSumErrorName]"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                        ></div>
                        <div
                            *ngIf="cathodeMaterialFormGroup.invalid && cathodeMaterialFormGroup.errors?.[groupSumErrorName]"
                            class="com-text-error col-12 xl:col-9 p-fluid mt-1"
                        >
                            <span class="mr-2"><i class="pi pi-times-circle"></i></span>
                            <span i18n="@@materials.percentages.error">Total of percentages must equal 100</span>
                        </div>
                    </div>
                    <div class="field grid">
                        <label
                            for="cathode_material2_id"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.input.label"
                        >
                            Material {{ 2 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-dropdown
                                name="cathode_material2_id"
                                inputId="cathode_material2_id"
                                [options]="remainingBlendableMaterials"
                                [(ngModel)]="design.cathodeMaterials[1].materialId"
                                optionLabel="name"
                                optionValue="id"
                            ></p-dropdown>
                        </div>
                    </div>

                    <div
                        class="field grid"
                        [class.hidden]="!materialMap.get(design.cathodeMaterials[1].materialId)?.length"
                    >
                        <label
                            for="cathode_material2_version_id"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.version.input.label"
                        >
                            Version Material {{ 2 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-dropdown
                                name="cathode_material2_version_id"
                                inputId="cathode_material2_version_id"
                                [options]="materialMap.get(design.cathodeMaterials[1].materialId)!"
                                [(ngModel)]="design.cathodeMaterials[1].materialVersionId"
                                optionLabel="description"
                                optionValue="id"
                                [disabled]="materialMap.get(design.cathodeMaterials[1].materialId)?.length === 1"
                            >
                                <ng-template let-selectedVersion pTemplate="selectedItem">
                                    <div>{{ selectedVersion.description }} - {{ selectedVersion.date | date }}</div>
                                </ng-template>
                                <ng-template let-version pTemplate="item">
                                    <div>{{ version.description }} - {{ version.date | date }}</div>
                                </ng-template></p-dropdown
                            >
                        </div>
                    </div>

                    <div class="field grid">
                        <label
                            [for]="controlNames.cathodeMaterial2Weight"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.ratio.input.label"
                        >
                            Anteil Material {{ 2 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.cathodeMaterial2Weight"
                                [inputId]="controlNames.cathodeMaterial2Weight"
                                [(ngModel)]="design.cathodeMaterials[1].weightPercent"
                                [maxFractionDigits]="2"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [max]="100"
                                [required]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeMaterialFormGroup.invalid
                                }"
                            ></p-inputNumber>
                        </div>
                        <div
                            *ngIf="cathodeMaterialFormGroup.invalid && cathodeMaterialFormGroup.errors?.[groupSumErrorName]"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                        ></div>
                        <div
                            *ngIf="cathodeMaterialFormGroup.invalid && cathodeMaterialFormGroup.errors?.[groupSumErrorName]"
                            class="com-text-error col-12 xl:col-9 p-fluid mt-1"
                        >
                            <span class="mr-2"><i class="pi pi-times-circle"></i></span>
                            <span i18n="@@materials.percentages.error">Total of percentages must equal 100</span>
                        </div>
                    </div>
                </ng-container>
                <ng-container
                    [ngModelGroup]="formGroupNames.cathodeQValues"
                    #cathodeQValuesFormGroup="ngModelGroup"
                    comValidateCathodeQValuesDirective
                >
                    <div class="field grid">
                        <label
                            for="cathode_q_aim"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.qAim.input.label"
                        >
                            Ziel Qrev C/10
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                name="cathode_q_aim"
                                inputId="cathode_q_aim"
                                [(ngModel)]="design.cathodeQAim"
                                #cathodeQAimField="ngModel"
                                mode="decimal"
                                [minFractionDigits]="2"
                                suffix=" mAh/g"
                                [min]="0.001"
                                [showClear]="true"
                                [required]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeQValuesFormGroup.invalid
                                }"
                            ></p-inputNumber>
                        </div>
                    </div>
                    <div class="field grid">
                        <label
                            for="cathode_q_aim_1"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.qAim1.input.label"
                        >
                            Q 1st Aim
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                name="cathode_q_aim_1"
                                inputId="cathode_q_aim_1"
                                [(ngModel)]="design.cathodeQAimFirstCharge"
                                #cathodeQAimFirstChargeField="ngModel"
                                mode="decimal"
                                [minFractionDigits]="2"
                                suffix=" mAh/g"
                                [min]="0.001"
                                [showClear]="true"
                                [required]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeQValuesFormGroup.invalid
                                }"
                            ></p-inputNumber>
                        </div>
                    </div>
                    <div *ngIf="cathodeQValuesFormGroup.invalid" class="col-12 mb-2 xl:col-3 xl:mb-0"></div>
                    <div *ngIf="cathodeQValuesFormGroup.invalid" class="com-text-error col-12 xl:col-9 p-fluid mt-1">
                        <span class="mr-2"><i class="pi pi-times-circle"></i></span>
                        <!-- Show required field errors first -->
                        <span
                            *ngIf="cathodeQAimField.errors?.['required'] && cathodeQAimField.touched"
                            i18n="@@swelling.fieldIsRequired"
                        >
                            Field is required.
                        </span>
                        <span
                            *ngIf="cathodeQAimFirstChargeField.errors?.['required'] && cathodeQAimFirstChargeField.touched && !cathodeQAimField.errors?.['required']"
                            i18n="@@swelling.fieldIsRequired"
                        >
                            Field is required.
                        </span>
                        <!-- Show custom validation errors only if no required field errors -->
                        <span
                            *ngIf="!cathodeQAimField.errors?.['required'] && !cathodeQAimFirstChargeField.errors?.['required'] && cathodeQValuesFormGroup.errors?.[cathodeQValuesMinimumError]"
                            i18n="@@cathode.qValues.minimumValue.error"
                        >
                            Both Target Qrev C/10 and Target Q1st C/10 values must be at least 150 mAh/g
                        </span>
                        <span
                            *ngIf="!cathodeQAimField.errors?.['required'] && !cathodeQAimFirstChargeField.errors?.['required'] && cathodeQValuesFormGroup.errors?.[cathodeQValuesComparisonError]"
                            i18n="@@cathode.qValues.comparison.error"
                        >
                            Target Q1st C/10 must be greater than Target Qrev C/10
                        </span>
                    </div>
                </ng-container>
            </ng-container>
        </p-card>

        <p-card>
            <div *ngIf="loading" class="flex justify-content-center">
                <p-progressSpinner></p-progressSpinner>
            </div>

            <com-summary-table
                *ngIf="metrics"
                [headers]="metrics.cathode.summary.headers"
                [rows]="metrics.cathode.summary.rows"
                [highlightedColumns]="highlightedColumns"
            ></com-summary-table>
        </p-card>
    </div>

    <div class="col-12 lg:col-6">
        <com-material-chart [data]="metrics?.cathode?.chart" [options]="options" [loading]="loading" />
    </div>
</div>

@use "../../../assets/styles/variables.scss" as var;

.grid {
    .com-text-error {
        margin-left: 2rem;

        .error-icon {
            margin-right: 0.5rem;
        }
    }
}

.css-grid {
    display: grid;
    justify-content: space-evenly;
    align-content: space-evenly;
    align-items: center;
    row-gap: 0.25rem;
    column-gap: 0.5rem;
    grid-auto-rows: minmax(2.5rem, auto);

    & .col-span-2 {
        grid-column: span 2;
    }

    & .col-span-4 {
        grid-column: 1 / -1;
    }

    & hr {
        grid-column: 1 / -1;
        margin: 0;
    }

    .flex-group {
        display: flex;
        align-items: center;

        .button-minus,
        .button-plus {
            padding-left: 0.5rem;
        }

        > * {
            flex-shrink: 1;
        }

        .apply-remove-button {
            padding: 0.5rem;
            color: var.$logoLightSecondaryColor;
        }
    }
}

@media (max-width: 1250px) {
    .css-grid {
        grid-template-columns: 3fr 1fr;

        & .col-span-2 {
            grid-column: span 1;
        }

        grid-auto-rows: minmax(1rem, auto);
    }

    .density-mobile {
        text-align: end;
        // We want to have this in this specific size as this is the width of a p-button
        margin-left: 2.357rem;
        // The same styles as col-12 xl:col-9 for this size
        width: 75%;
        padding: 0.5rem;
    }
}

@media (max-width: 1200px) {
    .density-mobile {
        margin-left: 0;
        width: 100%;
    }
}

@media (max-width: 610px) {
    .css-grid {
        grid-template-columns: 1fr 1fr;

        & .mobile-tiny-col-span-2 {
            grid-column: span 2;
        }

        grid-auto-rows: minmax(1rem, auto);
    }
}

@media (min-width: 1251px) {
    .css-grid {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PrimeNgModule } from '@com/app/prime-ng';
import { DesignMetaEditorComponent } from './component/design-meta-editor/design-meta-editor.component';
import { BoolTextPipe } from './pipe/bool-text.pipe';
import { InplaceEditComponent } from './component/inplace-edit/inplace-edit.component';
import { ConstrainValueDirective } from './directive/constrain-value.directive';
import { ValidateGroupSumDirective } from './directive/validators/validate-group-sum.directive';

@NgModule({
    imports: [FormsModule, CommonModule, PrimeNgModule],
    exports: [
        BoolTextPipe,
        DesignMetaEditorComponent,
        ConstrainValueDirective,
        InplaceEditComponent,
        ValidateGroupSumDirective,
    ],
    declarations: [
        BoolTextPipe,
        DesignMetaEditorComponent,
        ConstrainValueDirective,
        InplaceEditComponent,
        ValidateGroupSumDirective,
    ],
})
export class SharedModule {}

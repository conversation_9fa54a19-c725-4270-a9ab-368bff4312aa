{"locale": "de-DE", "translations": {"tab.toleranceAnalysis": "Toleranzanalyse", "createTolerance.noToleranceMessage": "Toleranzanalyse nicht verfügbar.", "createTolerance.parameterSet": "Parametersatz", "createTolerance.toleranceVariables": "Toleranzvariablen", "createTolerance.minimumValue": "Minimum Wert", "createTolerance.standardValue": "Standard Wert", "createTolerance.maximumValue": "Maximum Wert", "createTolerance.minimumValueValidation": "Minimalwert muss unter Höchstwert liegen", "createTolerance.missingStandardValue": "Der Wert für diese Toleranzvariable ist im Zelldesign nicht angegeben. Um die Toleranz zu berechnen, müssen Sie diesen Wert zuerst festlegen.", "createTolerance.calculateTolerance": "To<PERSON><PERSON><PERSON> be<PERSON>", "createTolerance.toleranceResults": "Toleranzergebnisse", "createTolerance.variableResults": "Variablenergebnisse", "createTolerance.valueType": "Wertetyp", "createTolerance.valueTypeAbsolute": "Absolut", "createTolerance.valueTypeRelative": "Relativ", "createTolerance.valueTypeRelativePercentage": "<PERSON><PERSON><PERSON>", "createTolerance.chainedResults": "Verkettete Ergebnisse", "createTolerance.minimumValueResult": "Minimum Wert", "createTolerance.standardValueResult": "Standard Wert", "createTolerance.maximumValueResult": "Maximum Wert", "createTolerance.calculating": "Toleranz wird berechnet...", "createTolerance.variableCategory": "Variablenkategorie", "createTolerance.resultsFor": "Ergebnisse für:", "axisTitle.capacityAnode": "Kapazität / mAh/g(AAM)", "axisTitle.capacityCathode": "Kapazität / mAh/g(CAM)", "axisTitle.cellPotential": "Zellpotential / V", "axisTitle.voltage": "Spannung vs. Li / V", "balancing.hysteresis.label": "Hysterese", "balancing.npRatioFirst.input.label": "N/P Verhältnis Formierung", "balancing.npRatioRev.label": "N/P Verhältnis Reversibel", "balancing.uMax.input.label": "Umax", "balancing.uMin.input.label": "<PERSON><PERSON>", "bom.input.dataSet.label": "Data Set", "bom.input.scrapMaterial.label": "Ausschuss Material", "cellDesign.topbar.designId": "DesignID: {$INTERPOLATION}", "cellDesign.topbar.name": "Name: {$INTERPOLATION}", "cellDesign.topbar.releaseStatus": "Freigabestatus: {$INTERPOLATION}", "cellDesign.topbar.partNumber": "Part Nummer: {$INTERPOLATION}", "cellDesign.topbar.capacityAh": "Kapazität: {$INTERPOLATION} Ah", "cellDesign.topbar.energyContentWh": "Energiegehalt: {$INTERPOLATION} Wh", "cellDesign.topbar.energyDensityVolumentric": "Energiedichte Volumetrisch: {$INTERPOLATION} Wh/l", "cellDesign.topbar.energyDensityGravimetric": "Energiedichte Gravimetrisch: {$INTERPOLATION} Wh/kg", "cellDesign.topbar.packageWeight": "Gesamte Zelle: {$INTERPOLATION} g", "cell.anodeLengthTotal": "Gesamtlänge Anode", "cell.anodeOverhang": "Anodenüberstand", "cell.anodeWindingCount": "<PERSON><PERSON>hl Windungen Anode", "cell.assemblyClearance": "Montagefreiraum", "cell.coatingSurfaces": "Beschichtungsflächen", "cell.caseWeight": "<PERSON><PERSON><PERSON><PERSON>ge<PERSON><PERSON> (inkl. Tabs)", "cell.cathodeLengthTotal": "Gesamtlänge Kathode", "cell.cathodeWindingCount": "<PERSON><PERSON>hl Windungen Kathode", "cell.cellCoreDiameter": "Zellkern Durchmesser", "cell.cellDiameter": "<PERSON><PERSON>", "cell.cellFormat": "Zellformat", "cell.cellHeight": "<PERSON><PERSON>", "cell.cellLayerDiameterMax": "Max. Gesamtdurchmesser Zelllagen", "cell.cellLayerDiameterTotal": "Gesamtdurchmesser Zelllagen", "cell.cellLength": "<PERSON><PERSON>", "cell.cellThickness": "<PERSON><PERSON>", "cell.cellType.cylinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cell.celltype.label": "Zelltyp", "cell.cellType.pouch": "Pouch", "cell.cellType.prisma": " Prismatisch", "cell.cellVolume": "Zellvolumen", "cell.cellWidth": "Zell B<PERSON>", "cell.chargePerLayerC10": "Ladung pro Zelllage C/1O", "cell.chargePerLayerC3": "Ladung pro Zelllage C/3", "cell.coatingAreaAnode": "Beschichtungsfläche Anode", "cell.coatingAreaCathode": "Beschichtungsfläche Kathode", "cell.coatingAreaSeparator": "Beschichtungsfläche Separator", "cell.coatingLengthAnode": "Beschichtungslänge Anode", "cell.coatingLengthCathode": "Beschichtungslänge Kathode", "cell.coatingLengthSeparator": "Beschichtungslänge Separator", "cell.coatingWidthAnode": "Beschichtungsbreite Anode", "cell.coatingWidthCathode": "Beschichtungsbreite Kathode", "cell.coatingWidthSeparator": "Beschichtungsbreite Separator", "cell.editFormat": "Format bearbeiten?", "cell.electrolyteSwelling": "Elektrolytswelling", "cell.energyPerLayerC10": "Energie pro Zelllage C/1O", "cell.energyPerLayerC3": "Energie pro Zelllage C/3", "cell.hasTwoSubstacks": "Zusammenbau aus 2 Substacks?", "cell.housingThickness": "Pouch/Gehäuse-Wandstärke", "cell.layerFreeSpace": "Freiraum Zelllagen", "cell.layerFreeSpaceOverThickness": "Freiraum Zelllagen / Dick<PERSON>lage", "cell.maxTotalLayerThickness": "<PERSON><PERSON><PERSON><PERSON>", "cell.numberOfActiveLayers": "Anzahl aktive Lagen", "cell.numberOfLayersAnode": "<PERSON><PERSON><PERSON> Lagen Anode", "cell.numberOfLayersCathode": "<PERSON><PERSON><PERSON> Lagen Kathode", "cell.numberOfLayersSeparator": "<PERSON><PERSON><PERSON> Lagen Separator", "cell.separatorWindingCount": "<PERSON><PERSON>hl Windungen Separator", "cell.swellingBuffer": "Swelling-Vorhalt", "cell.totalAreaSeparator": "Gesamtfläche Separator", "cell.totalLayerThickness": "Gesamtdicke Z<PERSON>lagen", "cell.cellGeometry": "Zellgeometrie", "cell.cellLayerCalculation": "Zelllagenberechnung", "chemicals.anode.prelithiationCapacity.input.label": "Prälithiierung", "chemicals.common.developerMode.checkbox.label": "Entwicklermodus", "chemicals.common.material.input.label": " Material {$INTERPOLATION} ", "chemicals.common.material.ratio.input.label": " Anteil Material {$INTERPOLATION} ", "chemicals.common.material.version.input.label": " Version Material {$INTERPOLATION} ", "chemicals.common.qAim.input.label": "Ziel Qrev C/10", "chemicals.common.qAim1.input.label": "Q 1st Aim", "common.aging": "Alterung", "common.amountOfElectrolyte": "Elektrolytmenge", "common.amperHour": "{$INTERPOLATION} Ah", "common.anode": "Anode", "common.anodeDensity": "Dichte Anode", "common.cancel": "Abbrechen", "common.create": "<PERSON><PERSON><PERSON><PERSON>", "common.capacity": "Kapazität", "cathode.qValues.comparison.error": "Ziel Q1st C/10 muss größer sein als Ziel Qrev C/10", "cathode.qValues.minimumValue.error": "Sowohl Ziel Qrev C/10 als auch Ziel Q1st C/10 Werte müssen mindestens 150 mAh/g betragen", "common.cathode": "Kathode", "common.cathodeDensity": "Dichte Kathode", "common.cellDimensions": "Zelldimensionen", "common.cellsTotal": "<PERSON><PERSON><PERSON> g<PERSON>t", "common.confirm": "<PERSON>a", "common.confirmation": "Bestätigen", "common.cRate10Symbol": "C/10", "common.cRate3Symbol": "C/3", "common.delete": "Löschen", "common.deleteConfirmationMessage": "Sind <PERSON> sicher, dass Si<PERSON> {$name} löschen möchten?", "common.density": "<PERSON><PERSON><PERSON>", "common.description": "Beschreibung", "common.design": "Design", "common.designAlreadyExitsts": "Design kann nicht gespeichert werden, da ein übereinstimmendes Design bereits existiert. Möchten Sie das existierende Design öffnen?", "common.designsCannotBeEdited": "Designs können nicht editiert werden. Möchten Sie stattdessen ein neues Design mit neuer ID speichern?", "common.editingItem": "Editing {$INTERPOLATION}", "common.editMetadata": "<PERSON><PERSON><PERSON> bearbeiten", "common.editOrRelease": "Metadaten bearbeiten/Freigeben", "common.electrolyteSeparator": "Electrolyt + Separator", "common.energyHold": "Energiegehalt", "common.discharge": "Entladen", "common.errorWithCode": "Fehlercode: {$statusCode}, Nachricht: {$message}", "common.errorWithMessage": "<PERSON>hler: {$message}", "common.euro": "{$INTERPOLATION} €", "common.euroPerKilowattHour": "{$INTERPOLATION} €/kWh", "common.export": "Export", "common.genericErrorMessage": "Ein Fehler ist aufgetreten: {$INTERPOLATION}", "common.validationErrorTitle": "Design Validierungsfehler", "common.validationErrorMessage": "Ihr Design enthält Validierungsfehler. Bitte beheben Sie die mit * markier<PERSON>hler, um Metriken zu berechnen.", "common.gram": "{$INTERPOLATION} g", "common.informationFromManufacturer": "aus Herstellerangaben", "common.itemHasBeenDeleted": "{$PH} wurde erfolgreich gelöscht!", "common.kilogram": "{$INTERPOLATION} kg", "common.kiloWatHour": "{$INTERPOLATION} kWh", "common.charge": "Laden", "common.litre": "{$INTERPOLATION} l", "common.materialPricePerKwH": "Materialpreis pro kWh", "common.microMeter": "{$INTERPOLATION} µm", "common.milliAmperHourPerSquareCentiMeter": "{$INTERPOLATION} mAh/cm²", "common.milliGramPerSquareCentiMeter": "{$INTERPOLATION} mg/cm²", "common.milliVolts": "{$INTERPOLATION} mV", "common.moduleCount": "<PERSON><PERSON><PERSON>", "common.name": "Name", "common.nominalVoltage": "<PERSON><PERSON><PERSON>", "common.notReleased": "Nicht freigegeben", "common.open": "<PERSON><PERSON><PERSON>", "common.outOfBalancing": "aus Balancing", "common.packDesign": "Pack Design", "common.packingWeight": "Packgewicht", "common.partNumber": "Part Nummer", "common.percentageNumber": "{$INTERPOLATION} %", "common.poreVolume": "Porenvolumen", "common.porosity": "Porosität", "common.price": "Pre<PERSON>", "common.projectName": "Projektname", "common.projectState": "Musterstand", "common.released": "Released", "common.releasedBy": "<PERSON><PERSON><PERSON><PERSON> von", "common.releasedDate": "Freigabendatum", "common.releaseStatus": "Frei<PERSON>ben<PERSON><PERSON>", "common.safety": "Sicherheit", "common.save": "Speichern", "common.saveNewDesign": "Save new design", "common.searchDesigns": "Search Designs", "common.soc": "SoC", "common.socMaterial": "SoC Material {$INTERPOLATION}", "common.startNewDesign": "Start new Design", "common.success": "Erfolgreich", "common.unexpectedError": "<PERSON><PERSON><PERSON><PERSON>", "common.units.amperHours": "{$INTERPOLATION} Ah", "common.units.micrometer": "{$INTERPOLATION} μm", "common.units.mililitre": "{$INTERPOLATION} ml", "common.units.mililitrePerAmperHour": "{$INTERPOLATION} ml/Ah", "common.units.millimeter": "{$INTERPOLATION} mm", "common.units.nanometersPerAmperhour": "{$INTERPOLATION} nm/Ah", "common.units.squareMeter": "{$INTERPOLATION} m²", "common.units.squareMillimeters": "{$INTERPOLATION} mm²", "common.units.percentages": "{$INTERPOLATION} %", "common.units.wattHours": "{$INTERPOLATION} Wh", "common.volt": "{$INTERPOLATION} V", "common.voltage": "Spannung", "common.voltageLithium": "<PERSON><PERSON><PERSON> vs. <PERSON>", "common.warning": "<PERSON><PERSON><PERSON>", "common.wattHour": "{$INTERPOLATION} Wh", "common.watthourPerKilogram": "{$INTERPOLATION} Wh/kg", "common.watthourPerLitre": "{$INTERPOLATION} Wh/l", "common.createdAt": "Erstelldatum", "common.createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "createOptimization.createNewOptimizedDesign": "Neues optimiertes Design erstellen", "createOptimization.noOptimizationMessage": "Optimierung der Anodenmaterialm ischung nicht verfügbar.", "createOptimization.parameterSet": "Parametersatz", "createOptimization.optimizationVariables": "Optimierungsvariablen", "createOptimization.lowerLimit": "Unteres Limit", "createOptimization.upperLimit": "Oberes Limit", "createOptimization.stepSize": "Limit <PERSON>hrittgrö<PERSON>", "createOptimization.objectiveFunction": "Zielfunktion", "createOptimization.optimizationAlgorithm": "Optimierungsalgorithmus", "createOptimization.optimizationObjective": "Optimierungsziel", "createOptimization.optimizationTarget": "Optimierungsfokus", "createOptimization.anodeWeightValidation": "Die Optimierung des Anodengewichts ist nicht möglich. Nur Mischungen mit Material der Gruppe 14 SCC55 sind zulässig.", "createOptimization.cathodeWeightValidation": "Die Optimierung des Kathodengewichts ist nicht möglich. Bitte verwenden Sie eine der folgenden Mischungen: BASF_NCM90_PC_BT98B (Material 1) mit entweder Mitrachem_LMFP (Material 2) oder LFP400_IBUtec (Material 2).", "createOptimization.cellCostValidation": "<PERSON><PERSON> Z<PERSON> verfügbar. Bitte lege all Preise im Bom-Editor fest", "createOptimization.swellingValidation": "Die Optimierung des Swelling-Ziels ist nicht möglich. Bitte aktiviere Swelling.", "createOptimization.lowerLimitValidation": "Minimalwert muss unter Höchstwert liegen", "createOptimization.stepSizeValidation": "Schrittgr<PERSON>ße muss kleiner sein als die Differenz zwischen Ober und Untergrenze", "createOptimization.advancedSettings": "Erweiterte Einstellungen", "createOptimization.numInitialPoints": "Anzahl der Anfangspunkte", "createOptimization.numIterations": "Anzahl der Iterationen", "createOptimization.warningMessageGrid": "Diese Optimierungskonfiguration kann länger dauern. Reduzieren Sie den Bereich der Variablenlimits (unteres und oberes Limit) oder erhöhen Sie die Schrittgröße, um den Vorgang zu beschleunigen.", "createOptimization.warningMessageBayesian": "Diese Optimierungskonfiguration kann länger dauern. Um den Prozess zu beschleunigen, reduzieren Sie die Anzahl der Anfangspunkte oder verringern Sie die Anzahl der Iterationen.", "electrodePair.aluminumFoilThickness.input.label": "<PERSON><PERSON>", "electrodePair.anodeDensity.input.label": "Dichte Anode", "electrodePair.anodeThickness.label": "<PERSON><PERSON>", "electrodePair.balancingAnodeCathode.label": "Balancing (Anode/Kathode)", "electrodePair.calendarDates.label": "Kalenderdaten", "electrodePair.cathodeDensity.input.label": "Dichte Kathode", "electrodePair.cathodeThickness.label": "<PERSON><PERSON>", "electrodePair.cellLayerThickness.label": "<PERSON><PERSON>", "electrodePair.chargeDensityAnode.label": "Ladungsdichte Anode", "electrodePair.chargeDensityCathode.input.label": "Ladungsdichte Kathode", "electrodePair.coatingAreaLayerThickness.label": "Beschichtungsflächendichte und Schichtdicke", "electrodePair.coatingThicknessAnode.label": "Beschichtungsdicke Anode", "electrodePair.coatingThicknessCathode.label": "Beschichtungsdicke Kathode", "electrodePair.copperFoilThickness.input.label": "<PERSON><PERSON>folie", "electrodePair.electrodeThickness.label": "Elektrodendicken", "electrodePair.loadingAnode.label": "Beladung Anode", "electrodePair.loadingCathode.label": "Beladung Kathode", "electrodePair.proposityAnode.label": "Porosität Anode", "electrodePair.proposityCathode.label": "Porosität Kathode", "electrodePair.separatorThickness.input.label": "<PERSON><PERSON>", "electrolyte.activeSurfaceAnode": "Aktive Oberfläche Anode", "electrolyte.electrolyteConsumptionThroughSeiFormation": "Elektrolytverbrauch durch SEI Bildung", "electrolyte.estimatedFromSeiReaction": "Abgeschätzt aus SEI-Reaktion", "electrolyte.firstCycleEfficiency": "First Cycle Efficiency", "electrolyte.seiGrowthPerCharge": "SEI Wachstum pro Ladung", "electrolyte.suggestedAmountOfElectrolyte": "Vorschlag für Elektrolytmenge (mit Puffer für Formierung)", "electrolyte.warning.message": "Erste Abschätzung Electrolytverbrauch durch SEI Reaktion", "electrolyte.warning.request": "Hier brauchen wir noch zusätzlichen Input!", "electrolyte.warning.source": "(Quelle: {$INTERPOLATION})", "energyContent.capacityAndEnergyContent": "Kapazität und Energiegehalt", "energyContent.cellVolumeOverall.label": "Volume gesam<PERSON> Z<PERSON>", "energyContent.cellWeightOverall.label": "Gewicht gesamte Zelle", "energyContent.energyDensityGravimetric": "Energiedichte gravimetrisch", "energyContent.energyDensityVolumentric": "Energiedichte volumetrisch", "energyContent.nominalVoltage.label": "<PERSON><PERSON><PERSON>", "energyContent.parallelCellsCount.label": "Zellen parallel", "energyContent.priceCell.label": "Materialpreis pro Celle", "energyContent.serialCellsCount.label": "<PERSON><PERSON><PERSON>", "energyContent.cellsPerModule.label": "Zellen pro Modul", "materials.activeMaterialWeight.input.label": "Aktivmaterial", "materials.aluminiumDensity": "Dichte Aluminium", "materials.anode.fullCellUtilization": "Ausnutzung Anode Vollzelle", "materials.anode.halfCellUtilization": "Ausnutzung An<PERSON>", "materials.binderWeight.input.label": "Binder", "materials.cathode.fullCellUtilization": "Ausnutzung Kathode Vollzelle", "materials.cathode.halfCellUtilization": "Ausnutzung Kathode Hal<PERSON>le", "materials.conductiveAdditive.input.label": "Conductive Additive Materialien", "materials.copperDensity": "<PERSON><PERSON><PERSON>", "materials.currentCollector.input.label": "Stromableiter", "materials.electrolyte.input.label": "Elektrolyt", "materials.electrolyteDensity": "Dichte Elektrolyt", "materials.group14.label": "Group 14", "materials.lithiumWeight.label": "Lithium", "materials.nanoscale.label": "Nanoscale", "materials.percentages.error": "Total of percentages must equal 100", "materials.prelithiationProcess.label": "Prälithiierungsprozess", "materials.separator.input.label": "Separator", "materials.separatorDensity": "Dichte Separator", "materials.separatorPorousness": "Porosität Separator", "materials.weightAfterPrelithiation.header": "Gewichtsprozente nach Prälithiierung", "parameterOptimization.afterOptimization": "Nach der Optimierung", "parameterOptimization.beforeOptimization": "Vor der Optimierung", "parameterOptimization.cellKPI": "Zell-KPI", "parameterOptimization.efficiencyGain": "Effizienzsteigerung", "parameterOptimization.chartType": "Diagrammtyp", "parameterOptimization.noOptimizationMessage": "<PERSON><PERSON><PERSON> dieses Zell Design steht aktuell keine Optimierung zur Verfügung.", "parameterOptimization.optimizationFailed": "Optimierung fehlgeschlagen. Bitte erstelle eine neue.", "parameterOptimization.optimizationOutcome": "Optimierungsergebnis", "parameterOptimization.optimizationRunning": "Optimierung im Gange. <PERSON><PERSON><PERSON> dieser abgeschlossen ist, werden die Ergebnisse hier angezeigt. <PERSON>te warte.", "parameterOptimization.optimizationSettings": "Optimierungseinstellungen", "parameterOptimization.optimizationVariable": "Optimierungsvariable", "parameterOptimization.timeRemaining": "Geschätzte verbleibende Zeit: {$INTERPOLATION}", "parameterOptimization.loading": "Laden", "swelling.assumptions": "<PERSON><PERSON><PERSON>", "swelling.cf3Absolut": "CF3 Absolute Atmung mit Kompression", "swelling.cf3compressed": "CF3-At<PERSON><PERSON> mit Kompression", "swelling.cf3uncompressed": "CF3 ungepresste Atmung", "swelling.compressibility": "Kompressibilität über den Druckbereich", "swelling.constant": "Swelling Konstante {$INTERPOLATION}", "swelling.constantPoreVolume": "Konstantes Porenvolumen", "swelling.expansionOfSCC": "Expansion von SCC gemäß Dilatometrie: a * SoL^b", "swelling.fieldIsRequired": "<PERSON>ld ist erforderlich.", "swelling.freeSpace": "Freiraum nach der Formierung", "swelling.layerFreeSpace": "Freiraum Zelllagen", "swelling.noCathode": "Keine Expansion oder Schrumpfung der Kathode", "swelling.parameters": "Swelling Parameter", "swelling.stackBreathing": "Stack-Breathing", "swelling.sol": "SoL_SCC = SoL_Anode bei SoC = 100%", "swelling.swellingCalculation": "Bestimmung des Zellbreathings", "swelling.swellingCalculations": "Bestimmung des Zellbreathings", "swelling.swellingDuringFormation": "Swelling während der Formierung", "swelling.totalBreathing": "Gesamtes Breathing pro <PERSON><PERSON><PERSON>", "swelling.wrongFormatAndMaterial": "Die Bestimmung des Zellbreathings ist nicht verfügbar, da das Zellformat nicht CF3 ist und das Anodenmaterial nicht korrekt ist. Bitte verwenden Sie das CF3-Format und stellen Sie sicher, dass das Anodenmaterial Group 14 SCC ist.", "swelling.wrongFormat": "Die Bestimmung des Zellbreathings ist nur für das CF3-Zellformat verfügbar. Bitte aktualisieren Sie das Zellformat, um die Berechnungen zu ermöglichen.", "swelling.wrongMaterial": "Die Bestimmung des Zellbreathings erfordert, dass das Anodenmaterial Group 14 SCC ist. Bitte wählen Sie Group 14 SCC als Anodenmaterial aus.", "swelling.wrongMaterialBlend": "Die Bestimmung des Zellbreathings erfordert, dass mindestens ein Anodenmaterial in einem Blend Group 14 SCC ist. Bitte stellen <PERSON>, dass mindestens eines der ausgewählten Materialien Group 14 SCC ist.", "tab.anode": "Anode", "tab.balancing": "Balancing", "tab.bom": "BOM", "tab.cathode": "Kathode", "tab.cell": "<PERSON><PERSON>", "tab.cellDesign": "Zell Design", "tab.cellSpecs": "Zell Specs", "tab.chemicalDesign": "Chemie Design", "tab.electrodePair": "Elektrodenpaar", "tab.electrolyte": "Elektrolyt", "tab.designAnalysis": "Designanalyse", "tab.energyContent??": "Energiegehalt", "tab.materials": "Materialien", "tab.socTool": "SOC Tool", "tab.thermalProjection": "Thermische Hochrechnung", "tab.cellOptimization": "Zell Optimierung", "tab.createOptimization": "<PERSON><PERSON>elle Optimierung", "tab.parameterOptimization": "Parameter Optimierung", "toast.optimizationCreated": "Optimiertes Design mit Name {$INTERPOLATION} erfolgreich erstellt", "toast.optimizationCompletedDesc": "Zelldesign wurde aktualisiert.", "toast.optimizationCompletedTitle": "Optimierungsprozess abgeschlossen", "toast.optimizationRunningDesc": "Optimierung noch nicht abgeschlossen. Bitte warte bis der Optimierungsprozess abgeschlossen ist.", "toast.optimizationRunningTitle": "Optimierungsprozess läuft noch", "createTolerance.histogramResults": "Verteilungsanalyse", "createTolerance.histogramMetric": "<PERSON><PERSON>", "createTolerance.chartTitle": "Verteilung der Werte", "createTolerance.chartXAxis": "Wertebereich", "createTolerance.chartYAxis": "Häufigkeit"}, "primeng": {"startsWith": "Starts with", "contains": "Contains", "notContains": "Not contains", "endsWith": "Ends with", "equals": "Equals", "notEquals": "Not equals", "noFilter": "No Filter", "lt": "Less than", "lte": "Less than or equal to", "gt": "Greater than", "gte": "Greater than or equal to", "is": "Is", "isNot": "Is not", "before": "Before", "after": "After", "dateIs": "Date is", "dateIsNot": "Date is not", "dateBefore": "Date is before", "dateAfter": "Date is after", "clear": "Clear", "apply": "Apply", "matchAll": "Match All", "matchAny": "Match Any", "addRule": "Add Rule", "removeRule": "Remove Rule", "accept": "Yes", "reject": "No", "choose": "<PERSON><PERSON>", "upload": "Upload", "cancel": "Cancel", "dayNames": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "dayNamesShort": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "dayNamesMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"], "monthNames": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "monthNamesShort": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "dateFormat": "dd.mm.yy", "firstDayOfWeek": 1, "today": "Today", "weekHeader": "Wk", "weak": "Weak", "medium": "Medium", "strong": "Strong", "passwordPrompt": "Enter a password", "emptyMessage": "No results found", "emptyFilterMessage": "No results found"}}